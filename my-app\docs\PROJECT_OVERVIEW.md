# Chat Application - Technical Overview

## Architecture

This is a modern real-time chat application built with a React frontend and Convex backend.

### Frontend Architecture
- **React 19** with TypeScript for type safety
- **Vite** for fast development and building
- **Tailwind CSS** for styling with custom design system
- **React Router** for client-side routing
- **Radix UI** for accessible UI primitives

### Backend Architecture
- **Convex** for database and serverless functions
- **Real-time subscriptions** for live message updates
- **Schema-based** database with TypeScript types
- **Local development** with hot reloading

## Key Components

### Authentication System
- Demo authentication for development
- User context management
- Protected routes

### Chat System
- Real-time message sending/receiving
- Multiple chat room support
- Message history persistence
- User presence tracking

### UI System
- Responsive design (mobile-first)
- Dark/light theme support
- Accessible components
- Consistent design tokens

## Database Schema

### Messages Table
- `content`: Message text
- `userId`: Sender identifier
- `userName`: Display name
- `chatId`: Room identifier
- `createdAt`: Timestamp

### Users Table
- `userId`: Unique identifier
- `name`: Display name
- `email`: User email
- `lastSeen`: Last activity timestamp

## Development Workflow

1. **Local Development**: Both frontend and backend run locally
2. **Hot Reloading**: Changes reflect immediately
3. **Type Safety**: Full TypeScript coverage
4. **Real-time Updates**: Live data synchronization

## Performance Considerations

- **Bundle Splitting**: Optimized chunks for faster loading
- **Tree Shaking**: Unused code elimination
- **CSS Optimization**: Tailwind purging
- **Real-time Efficiency**: Optimized Convex subscriptions
