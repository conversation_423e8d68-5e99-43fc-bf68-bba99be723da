# Chat Application

## Project Overview
A real-time chat application built with React, TypeScript, and Convex.


I'll help you build a professional Chat Application from scratch using the Convex template. Let's start by setting up the project structure and documentation.

## Project Structure
```
my-chat-app/
├── .convex/               # Convex configuration
├── public/                # Static files
├── src/
│   ├── assets/            # Images, fonts, etc.
│   ├── components/         # Reusable UI components
│   │   ├── auth/           # Authentication components
│   │   ├── chat/           # Chat components
│   │   └── ui/             # Generic UI components
│   ├── lib/                # Utility functions
│   ├── pages/              # Page components
│   ├── App.tsx             # Main App component
│   └── main.tsx            # Entry point
├── .env.local             # Environment variables
├── .gitignore
├── package.json
└── README.md
```

## Features
- User authentication (sign up, login, logout)
- Real-time messaging
- Multiple chat rooms
- Message history
- Online user status
- Responsive design

## Tech Stack
- **Frontend**: React 19, TypeScript, Vite, Tailwind CSS
- **Backend**: Convex (Database & Serverless Functions)
- **Authentication**: Convex Auth
- **Real-time**: Convex Subscriptions
- **Styling**: Tailwind CSS
- **Linting/Formatting**: ESLint, Prettier

## Development Setup
1. Install dependencies: `npm install`
2. Start development server: `npm run dev`
3. Open [http://localhost:3000](http://localhost:3000)

## Building for Production
1. Build the app: `npm run build`
2. Preview the build: `npm run preview`

## Deployment
This app is configured for deployment to Convex. Push your code to your Git repository and connect it to your Convex project.
