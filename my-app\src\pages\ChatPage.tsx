import { useParams } from 'react-router-dom';
import { useMutation, useQuery } from 'convex/react';
import { api } from '../../convex/_generated/api';
import type { Id } from '../../convex/_generated/dataModel';
import { useAuth } from '../lib/auth-context';
import { useState, useRef, useEffect } from 'react';

type Message = {
  _id: Id<'messages'>;
  _creationTime: number;
  content: string;
  userId: string;
  userName: string;
  chatId: string;
  createdAt: number;
};

export const ChatPage = () => {
  const { chatId = 'default' } = useParams<{ chatId?: string }>();
  const { user } = useAuth();
  const [message, setMessage] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Get messages for the current chat
  const messages = useQuery(api.myFunctions.getRecentMessages, { chatId }) || [];
  
  // Mutation to send a new message
  const sendMessage = useMutation(api.messages.send);
  
  // Update user's last seen time
  const updateLastSeen = useMutation(api.myFunctions.updateLastSeen);
  
  useEffect(() => {
    if (user) {
      updateLastSeen({
        userId: user._id,
        name: user.name,
        email: user.email || '',
      });
    }
  }, [user, updateLastSeen]);

  // Auto-scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!message.trim() || !user) return;

    try {
      await sendMessage({
        content: message,
        chatId,
        userId: user._id,
        userName: user.name,
      });
      setMessage('');
    } catch (error) {
      console.error('Error sending message:', error);
    }
  };

  return (
    <div className="flex flex-col h-full">
      <div className="border-b border-gray-200 p-4">
        <h2 className="text-xl font-semibold">
          {chatId === 'default' ? 'General Chat' : `Chat ${chatId}`}
        </h2>
      </div>
      
      <div className="flex-1 p-4 overflow-y-auto bg-gray-50">
        <div className="max-w-3xl mx-auto space-y-4">
          {messages.length === 0 ? (
            <div className="text-center text-gray-500 mt-8">
              No messages yet. Start the conversation!
            </div>
          ) : (
            messages.map((msg) => (
              <div 
                key={msg._id.toString()}
                className={`flex ${msg.userId === user?._id ? 'justify-end' : 'justify-start'}`}
              >
                <div 
                  className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                    msg.userId === user?._id 
                      ? 'bg-primary text-white' 
                      : 'bg-white border border-gray-200'
                  }`}
                >
                  <div className="font-medium text-sm mb-1">
                    {msg.userId === user?._id ? 'You' : msg.userName}
                  </div>
                  <div>{msg.content}</div>
                  <div className="text-xs opacity-70 mt-1 text-right">
                    {new Date(msg.createdAt).toLocaleTimeString()}
                  </div>
                </div>
              </div>
            ))
          )}
          <div ref={messagesEndRef} />
        </div>
      </div>
      
      <div className="border-t border-gray-200 p-4 bg-white">
        <div className="max-w-3xl mx-auto">
          <form onSubmit={handleSendMessage} className="flex items-center">
            <input
              type="text"
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder="Type a message..."
              className="flex-1 px-4 py-2 border border-gray-300 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              disabled={!user}
            />
            <button 
              type="submit"
              className="bg-primary text-white px-6 py-2 rounded-r-lg hover:bg-primary/90 transition-colors disabled:opacity-50"
              disabled={!message.trim() || !user}
            >
              Send
            </button>
          </form>
        </div>
      </div>
    </div>
  );
};

export default ChatPage;
