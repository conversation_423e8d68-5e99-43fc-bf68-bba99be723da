import { v } from "convex/values";
import { query, mutation } from "./_generated/server";
import { Doc, Id } from "./_generated/dataModel";

export const send = mutation({
  args: {
    content: v.string(),
    chatId: v.string(),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    // Get or create user
    const user = await ctx.db
      .query("users")
      .withIndex("by_user", (q) => q.eq("userId", identity.subject))
      .first();

    if (!user) {
      await ctx.db.insert("users", {
        userId: identity.subject,
        name: identity.name || "Anonymous",
        email: identity.email || "",
        lastSeen: Date.now(),
      });
    } else {
      await ctx.db.patch(user._id, { lastSeen: Date.now() });
    }

    // Create message
    const messageId = await ctx.db.insert("messages", {
      content: args.content,
      userId: identity.subject,
      userName: identity.name || "Anonymous",
      chatId: args.chatId,
      createdAt: Date.now(),
    });

    return messageId;
  },
});

export const list = query({
  args: {
    chatId: v.string(),
  },
  handler: async (ctx, args) => {
    const messages = await ctx.db
      .query("messages")
      .withIndex("by_created", (q) => q.eq("chatId", args.chatId))
      .order("asc")
      .collect();

    return messages;
  },
});

export const listRecent = query({
  args: {
    limit: v.number(),
  },
  handler: async (ctx, args) => {
    const messages = await ctx.db
      .query("messages")
      .order("desc")
      .take(args.limit);

    return messages.reverse();
  },
});
