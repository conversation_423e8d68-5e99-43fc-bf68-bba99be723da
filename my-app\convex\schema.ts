import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  messages: defineTable({
    content: v.string(),
    userId: v.string(),
    userName: v.string(),
    chatId: v.string(),
    createdAt: v.number(),
  })
    .index("by_chat", ["chatId"])
    .index("by_created", ["chatId", "createdAt"]),

  users: defineTable({
    userId: v.string(),
    name: v.string(),
    email: v.string(),
    lastSeen: v.number(),
  })
    .index("by_user", ["userId"])
    .index("by_email", ["email"]),
});
