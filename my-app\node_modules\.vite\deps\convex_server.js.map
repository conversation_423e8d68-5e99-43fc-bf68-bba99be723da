{"version": 3, "sources": ["../../convex/src/server/impl/syscall.ts", "../../convex/src/server/impl/actions_impl.ts", "../../convex/src/server/vector_search.ts", "../../convex/src/server/impl/validate.ts", "../../convex/src/server/impl/vector_search_impl.ts", "../../convex/src/server/impl/authentication_impl.ts", "../../convex/src/server/filter_builder.ts", "../../convex/src/server/impl/filter_builder_impl.ts", "../../convex/src/server/index_range_builder.ts", "../../convex/src/server/impl/index_range_builder_impl.ts", "../../convex/src/server/search_filter_builder.ts", "../../convex/src/server/impl/search_filter_builder_impl.ts", "../../convex/src/server/impl/query_impl.ts", "../../convex/src/server/impl/database_impl.ts", "../../convex/src/server/impl/scheduler_impl.ts", "../../convex/src/server/impl/storage_impl.ts", "../../convex/src/server/impl/registration_impl.ts", "../../convex/src/server/pagination.ts", "../../convex/src/server/cron.ts", "../../convex/src/server/router.ts", "../../convex/src/server/components/index.ts", "../../convex/src/server/schema.ts"], "sourcesContent": ["import { ConvexError } from \"../../values/errors.js\";\nimport { jsonToConvex } from \"../../values/value.js\";\n\ndeclare const Convex: {\n  syscall: (op: string, jsonArgs: string) => string;\n  asyncSyscall: (op: string, jsonArgs: string) => Promise<string>;\n  jsSyscall: (op: string, args: Record<string, any>) => any;\n};\n/**\n * Perform a syscall, taking in a JSON-encodable object as an argument, serializing with\n * JSON.stringify, calling into Rust, and then parsing the response as a JSON-encodable\n * value. If one of your arguments is a Convex value, you must call `convexToJson` on it\n * before passing it to this function, and if the return value has a Convex value, you're\n * also responsible for calling `jsonToConvex`: This layer only deals in JSON.\n */\n\nexport function performSyscall(op: string, arg: Record<string, any>): any {\n  if (typeof Convex === \"undefined\" || Convex.syscall === undefined) {\n    throw new Error(\n      \"The Convex database and auth objects are being used outside of a Convex backend. \" +\n        \"Did you mean to use `useQuery` or `useMutation` to call a Convex function?\",\n    );\n  }\n  const resultStr = Convex.syscall(op, JSON.stringify(arg));\n  return JSON.parse(resultStr);\n}\n\nexport async function performAsyncSyscall(\n  op: string,\n  arg: Record<string, any>,\n): Promise<any> {\n  if (typeof Convex === \"undefined\" || Convex.asyncSyscall === undefined) {\n    throw new Error(\n      \"The Convex database and auth objects are being used outside of a Convex backend. \" +\n        \"Did you mean to use `useQuery` or `useMutation` to call a Convex function?\",\n    );\n  }\n  let resultStr;\n  try {\n    resultStr = await Convex.asyncSyscall(op, JSON.stringify(arg));\n  } catch (e: any) {\n    // Rethrow the exception to attach stack trace starting from here.\n    // If the error came from JS it will include its own stack trace in the message.\n    // If it came from Rust it won't.\n\n    // This only happens if we're propagating ConvexErrors\n    if (e.data !== undefined) {\n      const rethrown = new ConvexError(e.message);\n      rethrown.data = jsonToConvex(e.data);\n      throw rethrown;\n    }\n    throw new Error(e.message);\n  }\n  return JSON.parse(resultStr);\n}\n\n/**\n * Call into a \"JS\" syscall. Like `performSyscall`, this calls a dynamically linked\n * function set up in the Convex function execution. Unlike `performSyscall`, the\n * arguments do not need to be JSON-encodable and neither does the return value.\n *\n * @param op\n * @param arg\n * @returns\n */\nexport function performJsSyscall(op: string, arg: Record<string, any>): any {\n  if (typeof Convex === \"undefined\" || Convex.jsSyscall === undefined) {\n    throw new Error(\n      \"The Convex database and auth objects are being used outside of a Convex backend. \" +\n        \"Did you mean to use `useQuery` or `useMutation` to call a Convex function?\",\n    );\n  }\n  return Convex.jsSyscall(op, arg);\n}\n", "import { convexTo<PERSON>son, jsonToConvex, Value } from \"../../values/index.js\";\nimport { version } from \"../../index.js\";\nimport { performAsyncSyscall } from \"./syscall.js\";\nimport { parseArgs } from \"../../common/index.js\";\nimport { FunctionReference } from \"../../server/api.js\";\nimport { getFunctionAddress } from \"../components/paths.js\";\n\nfunction syscallArgs(\n  requestId: string,\n  functionReference: any,\n  args?: Record<string, Value>,\n) {\n  const address = getFunctionAddress(functionReference);\n  return {\n    ...address,\n    args: convexToJson(parseArgs(args)),\n    version,\n    requestId,\n  };\n}\n\nexport function setupActionCalls(requestId: string) {\n  return {\n    runQuery: async (\n      query: FunctionReference<\"query\", \"public\" | \"internal\">,\n      args?: Record<string, Value>,\n    ): Promise<any> => {\n      const result = await performAsyncSyscall(\n        \"1.0/actions/query\",\n        syscallArgs(requestId, query, args),\n      );\n      return jsonToConvex(result);\n    },\n    runMutation: async (\n      mutation: FunctionReference<\"mutation\", \"public\" | \"internal\">,\n      args?: Record<string, Value>,\n    ): Promise<any> => {\n      const result = await performAsyncSyscall(\n        \"1.0/actions/mutation\",\n        syscallArgs(requestId, mutation, args),\n      );\n      return jsonToConvex(result);\n    },\n    runAction: async (\n      action: FunctionReference<\"action\", \"public\" | \"internal\">,\n      args?: Record<string, Value>,\n    ): Promise<any> => {\n      const result = await performAsyncSyscall(\n        \"1.0/actions/action\",\n        syscallArgs(requestId, action, args),\n      );\n      return jsonToConvex(result);\n    },\n  };\n}\n", "import { Id, Value } from \"../values/value.js\";\nimport {\n  DocumentByInfo,\n  FieldTypeFromFieldPath,\n  GenericDataModel,\n  GenericDocument,\n  GenericTableInfo,\n  GenericVectorIndexConfig,\n  NamedTableInfo,\n  NamedVectorIndex,\n  TableNamesInDataModel,\n  VectorIndexNames,\n} from \"./data_model.js\";\n\n/**\n * An object with parameters for performing a vector search against a vector index.\n * @public\n */\nexport interface VectorSearchQuery<\n  TableInfo extends GenericTableInfo,\n  IndexName extends VectorIndexNames<TableInfo>,\n> {\n  /**\n   * The query vector.\n   *\n   * This must have the same length as the `dimensions` of the index.\n   * This vector search will return the IDs of the documents most similar to\n   * this vector.\n   */\n  vector: number[];\n  /**\n   * The number of results to return. If specified, must be between 1 and 256\n   * inclusive.\n   *\n   * @default 10\n   */\n  limit?: number;\n  /**\n   * Optional filter expression made up of `q.or` and `q.eq` operating\n   * over the filter fields of the index.\n   *\n   * e.g. `filter: q => q.or(q.eq(\"genre\", \"comedy\"), q.eq(\"genre\", \"drama\"))`\n   *\n   * @param q\n   * @returns\n   */\n  filter?: (\n    q: VectorFilterBuilder<\n      DocumentByInfo<TableInfo>,\n      NamedVectorIndex<TableInfo, IndexName>\n    >,\n  ) => FilterExpression<boolean>;\n}\n\nexport type VectorSearch<\n  DataModel extends GenericDataModel,\n  TableName extends TableNamesInDataModel<DataModel>,\n  IndexName extends VectorIndexNames<NamedTableInfo<DataModel, TableName>>,\n> = (\n  tableName: TableName,\n  indexName: IndexName,\n  query: VectorSearchQuery<NamedTableInfo<DataModel, TableName>, IndexName>,\n) => Promise<Array<{ _id: Id<TableName>; _score: number }>>;\n\n/**\n * Expressions are evaluated to produce a {@link values.Value} in the course of executing a query.\n *\n * To construct an expression, use the {@link VectorFilterBuilder} provided within\n * {@link VectorSearchQuery}.\n *\n * @typeParam T - The type that this expression evaluates to.\n * @public\n */\nexport abstract class FilterExpression<T extends Value | undefined> {\n  // Property for nominal type support.\n  private _isExpression: undefined;\n\n  // Property to distinguish expressions by the type they resolve to.\n  private _value!: T;\n\n  /**\n   * @internal\n   */\n  constructor() {\n    // only defining the constructor so we can mark it as internal and keep\n    // it out of the docs.\n  }\n}\n\n/**\n * An interface for defining filters for vector searches.\n *\n * This has a similar interface to {@link FilterBuilder}, which is used in\n * database queries, but supports only the methods that can be efficiently\n * done in a vector search.\n *\n * @public\n */\nexport interface VectorFilterBuilder<\n  Document extends GenericDocument,\n  VectorIndexConfig extends GenericVectorIndexConfig,\n> {\n  //  Comparisons  /////////////////////////////////////////////////////////////\n\n  /**\n   * Is the field at `fieldName` equal to `value`\n   *\n   * @public\n   * */\n  eq<FieldName extends VectorIndexConfig[\"filterFields\"]>(\n    fieldName: FieldName,\n    value: FieldTypeFromFieldPath<Document, FieldName>,\n  ): FilterExpression<boolean>;\n\n  //  Logic  ///////////////////////////////////////////////////////////////////\n\n  /**\n   * `exprs[0] || exprs[1] || ... || exprs[n]`\n   *\n   * @public\n   */\n  or(...exprs: Array<FilterExpression<boolean>>): FilterExpression<boolean>;\n}\n", "export function validateArg(\n  arg: any,\n  idx: number,\n  method: string,\n  argName: string,\n) {\n  if (arg === undefined) {\n    throw new TypeError(\n      `Must provide arg ${idx} \\`${argName}\\` to \\`${method}\\``,\n    );\n  }\n}\n\nexport function validateArgIsInteger(\n  arg: any,\n  idx: number,\n  method: string,\n  argName: string,\n) {\n  if (!Number.isInteger(arg)) {\n    throw new TypeError(\n      `Arg ${idx} \\`${argName}\\` to \\`${method}\\` must be an integer`,\n    );\n  }\n}\n\nexport function validateArgIsNonNegativeInteger(\n  arg: any,\n  idx: number,\n  method: string,\n  argName: string,\n) {\n  if (!Number.isInteger(arg) || arg < 0) {\n    throw new TypeError(\n      `Arg ${idx} \\`${argName}\\` to \\`${method}\\` must be a non-negative integer`,\n    );\n  }\n}\n", "import { J<PERSON>NValue } from \"../../values/index.js\";\nimport { performAsyncSyscall } from \"./syscall.js\";\nimport { version } from \"../../index.js\";\nimport {\n  FilterExpression,\n  VectorFilterBuilder,\n  VectorSearch,\n  VectorSearchQuery,\n} from \"../vector_search.js\";\nimport {\n  FieldTypeFromFieldPath,\n  GenericDataModel,\n  GenericDocument,\n  GenericTableInfo,\n  GenericVectorIndexConfig,\n} from \"../data_model.js\";\nimport { validateArg } from \"./validate.js\";\nimport { Value, convexOrUndefinedToJson } from \"../../values/value.js\";\n\nexport function setupActionVectorSearch(\n  requestId: string,\n): VectorSearch<GenericDataModel, string, string> {\n  return async (\n    tableName: string,\n    indexName: string,\n    query: VectorSearchQuery<GenericTableInfo, string>,\n  ) => {\n    validateArg(tableName, 1, \"vectorSearch\", \"tableName\");\n    validateArg(indexName, 2, \"vectorSearch\", \"indexName\");\n    validateArg(query, 3, \"vectorSearch\", \"query\");\n    if (\n      !query.vector ||\n      !Array.isArray(query.vector) ||\n      query.vector.length === 0\n    ) {\n      throw Error(\"`vector` must be a non-empty Array in vectorSearch\");\n    }\n\n    return await new VectorQueryImpl(\n      requestId,\n      tableName + \".\" + indexName,\n      query,\n    ).collect();\n  };\n}\n\nexport class VectorQueryImpl {\n  private requestId: string;\n  private state:\n    | { type: \"preparing\"; query: SerializedVectorQuery }\n    | { type: \"consumed\" };\n\n  constructor(\n    requestId: string,\n    indexName: string,\n    query: VectorSearchQuery<GenericTableInfo, string>,\n  ) {\n    this.requestId = requestId;\n    const filters = query.filter\n      ? serializeExpression(query.filter(filterBuilderImpl))\n      : null;\n\n    this.state = {\n      type: \"preparing\",\n      query: {\n        indexName,\n        limit: query.limit,\n        vector: query.vector,\n        expressions: filters,\n      },\n    };\n  }\n\n  async collect(): Promise<Array<any>> {\n    if (this.state.type === \"consumed\") {\n      throw new Error(\"This query is closed and can't emit any more values.\");\n    }\n    const query = this.state.query;\n    this.state = { type: \"consumed\" };\n\n    const { results } = await performAsyncSyscall(\"1.0/actions/vectorSearch\", {\n      requestId: this.requestId,\n      version,\n      query,\n    });\n    return results;\n  }\n}\n\ntype SerializedVectorQuery = {\n  indexName: string;\n  limit?: number;\n  vector: Array<number>;\n  expressions: JSONValue;\n};\n\ntype ExpressionOrValue<T extends Value | undefined> = FilterExpression<T> | T;\n\n// The `any` type parameter in `Expression<any>` allows us to use this class\n// in place of any `Expression` type in `filterBuilderImpl`.\nexport class ExpressionImpl extends FilterExpression<any> {\n  private inner: JSONValue;\n  constructor(inner: JSONValue) {\n    super();\n    this.inner = inner;\n  }\n\n  serialize(): JSONValue {\n    return this.inner;\n  }\n}\n\nexport function serializeExpression(\n  expr: ExpressionOrValue<Value | undefined>,\n): JSONValue {\n  if (expr instanceof ExpressionImpl) {\n    return expr.serialize();\n  } else {\n    // Assume that the expression is a literal Convex value, which we'll serialize\n    // to its JSON representation.\n    return { $literal: convexOrUndefinedToJson(expr as Value | undefined) };\n  }\n}\n\nexport const filterBuilderImpl: VectorFilterBuilder<\n  GenericDocument,\n  GenericVectorIndexConfig\n> = {\n  //  Comparisons  /////////////////////////////////////////////////////////////\n\n  eq<FieldName extends GenericVectorIndexConfig[\"filterFields\"]>(\n    fieldName: FieldName,\n    value: FieldTypeFromFieldPath<GenericDocument, FieldName>,\n  ): FilterExpression<boolean> {\n    if (typeof fieldName !== \"string\") {\n      throw new Error(\"The first argument to `q.eq` must be a field name.\");\n    }\n    return new ExpressionImpl({\n      $eq: [\n        serializeExpression(new ExpressionImpl({ $field: fieldName })),\n        serializeExpression(value),\n      ],\n    });\n  },\n\n  //  Logic  ///////////////////////////////////////////////////////////////////\n\n  or(...exprs: Array<ExpressionOrValue<boolean>>): FilterExpression<boolean> {\n    return new ExpressionImpl({ $or: exprs.map(serializeExpression) });\n  },\n};\n", "import { Auth } from \"../authentication.js\";\nimport { performAsyncSyscall } from \"./syscall.js\";\n\nexport function setupAuth(requestId: string): Auth {\n  return {\n    getUserIdentity: async () => {\n      return await performAsyncSyscall(\"1.0/getUserIdentity\", {\n        requestId,\n      });\n    },\n  };\n}\n", "import { Value, NumericValue } from \"../values/index.js\";\nimport {\n  DocumentByInfo,\n  FieldPaths,\n  FieldTypeFromFieldPath,\n  GenericTableInfo,\n} from \"./data_model.js\";\n\n/**\n * Expressions are evaluated to produce a {@link values.Value} in the course of executing a query.\n *\n * To construct an expression, use the {@link FilterBuilder} provided within\n * {@link OrderedQuery.filter}.\n *\n * @typeParam T - The type that this expression evaluates to.\n * @public\n */\nexport abstract class Expression<T extends Value | undefined> {\n  // Property for nominal type support.\n  private _isExpression: undefined;\n\n  // Property to distinguish expressions by the type they resolve to.\n  private _value!: T;\n\n  /**\n   * @internal\n   */\n  constructor() {\n    // only defining the constructor so we can mark it as internal and keep\n    // it out of the docs.\n  }\n}\n/**\n * An {@link Expression} or a constant {@link values.Value}\n *\n * @public\n */\nexport type ExpressionOrValue<T extends Value | undefined> = Expression<T> | T;\n\n/**\n * An interface for defining filters in queries.\n *\n * `FilterBuilder` has various methods that produce {@link Expression}s.\n * These expressions can be nested together along with constants to express\n * a filter predicate.\n *\n * `FilterBuilder` is used within {@link OrderedQuery.filter} to create query\n * filters.\n *\n * Here are the available methods:\n *\n * |                               |                                               |\n * |-------------------------------|-----------------------------------------------|\n * | **Comparisons**               | Error when `l` and `r` are not the same type. |\n * | [`eq(l, r)`](#eq)             | `l === r`                                     |\n * | [`neq(l, r)`](#neq)           | `l !== r`                                     |\n * | [`lt(l, r)`](#lt)             | `l < r`                                       |\n * | [`lte(l, r)`](#lte)           | `l <= r`                                      |\n * | [`gt(l, r)`](#gt)             | `l > r`                                       |\n * | [`gte(l, r)`](#gte)           | `l >= r`                                      |\n * |                               |                                               |\n * | **Arithmetic**                | Error when `l` and `r` are not the same type. |\n * | [`add(l, r)`](#add)           | `l + r`                                       |\n * | [`sub(l, r)`](#sub)           | `l - r`                                       |\n * | [`mul(l, r)`](#mul)           | `l * r`                                       |\n * | [`div(l, r)`](#div)           | `l / r`                                       |\n * | [`mod(l, r)`](#mod)           | `l % r`                                       |\n * | [`neg(x)`](#neg)              | `-x`                                          |\n * |                               |                                               |\n * | **Logic**                     | Error if any param is not a `bool`.           |\n * | [`not(x)`](#not)              | `!x`                                          |\n * | [`and(a, b, ..., z)`](#and)   | `a && b && ... && z`                          |\n * | [`or(a, b, ..., z)`](#or)     | <code>a &#124;&#124; b &#124;&#124; ... &#124;&#124; z</code> |\n * |                               |                                               |\n * | **Other**                     |                                               |\n * | [`field(fieldPath)`](#field)  | Evaluates to the field at `fieldPath`.        |\n * @public\n */\nexport interface FilterBuilder<TableInfo extends GenericTableInfo> {\n  //  Comparisons  /////////////////////////////////////////////////////////////\n\n  /**\n   * `l === r`\n   *\n   * @public\n   * */\n  eq<T extends Value | undefined>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<boolean>;\n\n  /**\n   * `l !== r`\n   *\n   * @public\n   * */\n  neq<T extends Value | undefined>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<boolean>;\n\n  /**\n   * `l < r`\n   *\n   * @public\n   */\n  lt<T extends Value>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<boolean>;\n\n  /**\n   * `l <= r`\n   *\n   * @public\n   */\n  lte<T extends Value>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<boolean>;\n\n  /**\n   * `l > r`\n   *\n   * @public\n   */\n  gt<T extends Value>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<boolean>;\n\n  /**\n   * `l >= r`\n   *\n   * @public\n   */\n  gte<T extends Value>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<boolean>;\n\n  //  Arithmetic  //////////////////////////////////////////////////////////////\n\n  /**\n   * `l + r`\n   *\n   * @public\n   */\n  add<T extends NumericValue>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<T>;\n\n  /**\n   * `l - r`\n   *\n   * @public\n   */\n  sub<T extends NumericValue>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<T>;\n\n  /**\n   * `l * r`\n   *\n   * @public\n   */\n  mul<T extends NumericValue>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<T>;\n\n  /**\n   * `l / r`\n   *\n   * @public\n   */\n  div<T extends NumericValue>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<T>;\n\n  /**\n   * `l % r`\n   *\n   * @public\n   */\n  mod<T extends NumericValue>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<T>;\n\n  /**\n   * `-x`\n   *\n   * @public\n   */\n  neg<T extends NumericValue>(x: ExpressionOrValue<T>): Expression<T>;\n\n  //  Logic  ///////////////////////////////////////////////////////////////////\n\n  /**\n   * `exprs[0] && exprs[1] && ... && exprs[n]`\n   *\n   * @public\n   */\n  and(...exprs: Array<ExpressionOrValue<boolean>>): Expression<boolean>;\n\n  /**\n   * `exprs[0] || exprs[1] || ... || exprs[n]`\n   *\n   * @public\n   */\n  or(...exprs: Array<ExpressionOrValue<boolean>>): Expression<boolean>;\n\n  /**\n   * `!x`\n   *\n   * @public\n   */\n  not(x: ExpressionOrValue<boolean>): Expression<boolean>;\n\n  //  Other  ///////////////////////////////////////////////////////////////////\n\n  /**\n   * Evaluates to the field at the given `fieldPath`.\n   *\n   * For example, in {@link OrderedQuery.filter} this can be used to examine the values being filtered.\n   *\n   * #### Example\n   *\n   * On this object:\n   * ```\n   * {\n   *   \"user\": {\n   *     \"isActive\": true\n   *   }\n   * }\n   * ```\n   *\n   * `field(\"user.isActive\")` evaluates to `true`.\n   *\n   * @public\n   */\n  field<FieldPath extends FieldPaths<TableInfo>>(\n    fieldPath: FieldPath,\n  ): Expression<FieldTypeFromFieldPath<DocumentByInfo<TableInfo>, FieldPath>>;\n}\n", "import { JSONValue, Value, NumericValue } from \"../../values/index.js\";\nimport { convexOrUndefinedTo<PERSON><PERSON> } from \"../../values/value.js\";\nimport { GenericTableInfo } from \"../data_model.js\";\nimport {\n  Expression,\n  ExpressionOrValue,\n  FilterBuilder,\n} from \"../filter_builder.js\";\n\n// The `any` type parameter in `Expression<any>` allows us to use this class\n// in place of any `Expression` type in `filterBuilderImpl`.\nexport class ExpressionImpl extends Expression<any> {\n  private inner: JSONValue;\n  constructor(inner: JSONValue) {\n    super();\n    this.inner = inner;\n  }\n\n  serialize(): JSONValue {\n    return this.inner;\n  }\n}\n\nexport function serializeExpression(\n  expr: ExpressionOrValue<Value | undefined>,\n): JSONValue {\n  if (expr instanceof ExpressionImpl) {\n    return expr.serialize();\n  } else {\n    // Assume that the expression is a literal Convex value, which we'll serialize\n    // to its JSON representation.\n    return { $literal: convexOrUndefinedToJson(expr as Value | undefined) };\n  }\n}\n\nexport const filterBuilderImpl: FilterBuilder<GenericTableInfo> = {\n  //  Comparisons  /////////////////////////////////////////////////////////////\n\n  eq<T extends Value | undefined>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<boolean> {\n    return new ExpressionImpl({\n      $eq: [serializeExpression(l), serializeExpression(r)],\n    });\n  },\n\n  neq<T extends Value | undefined>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<boolean> {\n    return new ExpressionImpl({\n      $neq: [serializeExpression(l), serializeExpression(r)],\n    });\n  },\n\n  lt<T extends Value>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<boolean> {\n    return new ExpressionImpl({\n      $lt: [serializeExpression(l), serializeExpression(r)],\n    });\n  },\n\n  lte<T extends Value>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<boolean> {\n    return new ExpressionImpl({\n      $lte: [serializeExpression(l), serializeExpression(r)],\n    });\n  },\n\n  gt<T extends Value>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<boolean> {\n    return new ExpressionImpl({\n      $gt: [serializeExpression(l), serializeExpression(r)],\n    });\n  },\n\n  gte<T extends Value>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<boolean> {\n    return new ExpressionImpl({\n      $gte: [serializeExpression(l), serializeExpression(r)],\n    });\n  },\n\n  //  Arithmetic  //////////////////////////////////////////////////////////////\n\n  add<T extends NumericValue>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<T> {\n    return new ExpressionImpl({\n      $add: [serializeExpression(l), serializeExpression(r)],\n    });\n  },\n\n  sub<T extends NumericValue>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<T> {\n    return new ExpressionImpl({\n      $sub: [serializeExpression(l), serializeExpression(r)],\n    });\n  },\n\n  mul<T extends NumericValue>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<T> {\n    return new ExpressionImpl({\n      $mul: [serializeExpression(l), serializeExpression(r)],\n    });\n  },\n\n  div<T extends NumericValue>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<T> {\n    return new ExpressionImpl({\n      $div: [serializeExpression(l), serializeExpression(r)],\n    });\n  },\n\n  mod<T extends NumericValue>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<T> {\n    return new ExpressionImpl({\n      $mod: [serializeExpression(l), serializeExpression(r)],\n    });\n  },\n\n  neg<T extends NumericValue>(x: ExpressionOrValue<T>): Expression<T> {\n    return new ExpressionImpl({ $neg: serializeExpression(x) });\n  },\n\n  //  Logic  ///////////////////////////////////////////////////////////////////\n\n  and(...exprs: Array<ExpressionOrValue<boolean>>): Expression<boolean> {\n    return new ExpressionImpl({ $and: exprs.map(serializeExpression) });\n  },\n\n  or(...exprs: Array<ExpressionOrValue<boolean>>): Expression<boolean> {\n    return new ExpressionImpl({ $or: exprs.map(serializeExpression) });\n  },\n\n  not(x: ExpressionOrValue<boolean>): Expression<boolean> {\n    return new ExpressionImpl({ $not: serializeExpression(x) });\n  },\n\n  //  Other  ///////////////////////////////////////////////////////////////////\n  field(fieldPath: string): Expression<any> {\n    return new ExpressionImpl({ $field: fieldPath });\n  },\n};\n", "import {\n  GenericIndexFields,\n  GenericDocument,\n  FieldTypeFromFieldPath,\n} from \"./data_model.js\";\n\n/**\n * A type that adds 1 to a number literal type (up to 14).\n *\n * This is necessary to step through the fields in an index.\n */\ntype PlusOne<N extends number> = [\n  1,\n  2,\n  3,\n  4,\n  5,\n  6,\n  7,\n  8,\n  9,\n  10,\n  11,\n  12,\n  13,\n  14,\n  15,\n][N];\n\n/**\n * Builder to define an index range to query.\n *\n * An index range is a description of which documents Convex should consider\n * when running the query.\n *\n * An index range is always a chained list of:\n * 1. 0 or more equality expressions defined with `.eq`.\n * 2. [Optionally] A lower bound expression defined with `.gt` or `.gte`.\n * 3. [Optionally] An upper bound expression defined with `.lt` or `.lte`.\n *\n * **You must step through fields in index order.**\n *\n * Each equality expression must compare a different index field, starting from\n * the beginning and in order. The upper and lower bounds must follow the\n * equality expressions and compare the next field.\n *\n * For example, if there is an index of messages on\n * `[\"projectId\", \"priority\"]`, a range searching for \"messages in 'myProjectId'\n * with priority at least 100\" would look like:\n * ```ts\n * q.eq(\"projectId\", myProjectId)\n *  .gte(\"priority\", 100)\n * ```\n *\n * **The performance of your query is based on the specificity of the range.**\n *\n * This class is designed to only allow you to specify ranges that Convex can\n * efficiently use your index to find. For all other filtering use\n * {@link OrderedQuery.filter}.\n *\n * To learn about indexes, see [Indexes](https://docs.convex.dev/using/indexes).\n * @public\n */\nexport interface IndexRangeBuilder<\n  Document extends GenericDocument,\n  IndexFields extends GenericIndexFields,\n  FieldNum extends number = 0,\n> extends LowerBoundIndexRangeBuilder<Document, IndexFields[FieldNum]> {\n  /**\n   * Restrict this range to documents where `doc[fieldName] === value`.\n   *\n   * @param fieldName - The name of the field to compare. Must be the next field\n   * in the index.\n   * @param value - The value to compare against.\n   */\n  eq(\n    fieldName: IndexFields[FieldNum],\n    value: FieldTypeFromFieldPath<Document, IndexFields[FieldNum]>,\n  ): NextIndexRangeBuilder<Document, IndexFields, FieldNum>;\n}\n\n/**\n * An {@link IndexRangeBuilder} for the next field of the index.\n *\n * This type is careful to check if adding one to the `FieldNum` will exceed\n * the length of the `IndexFields`.\n */\ntype NextIndexRangeBuilder<\n  Document extends GenericDocument,\n  IndexFields extends GenericIndexFields,\n  FieldNum extends number,\n> =\n  PlusOne<FieldNum> extends IndexFields[\"length\"]\n    ? IndexRange\n    : IndexRangeBuilder<Document, IndexFields, PlusOne<FieldNum>>;\n\n/**\n * Builder to define the lower bound of an index range.\n *\n * See {@link IndexRangeBuilder}.\n *\n * @public\n */\nexport interface LowerBoundIndexRangeBuilder<\n  Document extends GenericDocument,\n  IndexFieldName extends string,\n> extends UpperBoundIndexRangeBuilder<Document, IndexFieldName> {\n  /**\n   * Restrict this range to documents where `doc[fieldName] > value`.\n   *\n   * @param fieldName - The name of the field to compare. Must be the next field\n   * in the index.\n   * @param value - The value to compare against.\n   */\n  gt(\n    fieldName: IndexFieldName,\n    value: FieldTypeFromFieldPath<Document, IndexFieldName>,\n  ): UpperBoundIndexRangeBuilder<Document, IndexFieldName>;\n  /**\n   * Restrict this range to documents where `doc[fieldName] >= value`.\n   *\n   * @param fieldName - The name of the field to compare. Must be the next field\n   * in the index.\n   * @param value - The value to compare against.\n   */\n  gte(\n    fieldName: IndexFieldName,\n    value: FieldTypeFromFieldPath<Document, IndexFieldName>,\n  ): UpperBoundIndexRangeBuilder<Document, IndexFieldName>;\n}\n\n/**\n * Builder to define the upper bound of an index range.\n *\n * See {@link IndexRangeBuilder}.\n *\n * @public\n */\nexport interface UpperBoundIndexRangeBuilder<\n  Document extends GenericDocument,\n  IndexFieldName extends string,\n> extends IndexRange {\n  /**\n   * Restrict this range to documents where `doc[fieldName] < value`.\n   *\n   * @param fieldName - The name of the field to compare. Must be the same index\n   * field used in the lower bound (`.gt` or `.gte`) or the next field if no\n   * lower bound was specified.\n   * @param value - The value to compare against.\n   */\n  lt(\n    fieldName: IndexFieldName,\n    value: FieldTypeFromFieldPath<Document, IndexFieldName>,\n  ): IndexRange;\n\n  /**\n   * Restrict this range to documents where `doc[fieldName] <= value`.\n   *\n   * @param fieldName - The name of the field to compare. Must be the same index\n   * field used in the lower bound (`.gt` or `.gte`) or the next field if no\n   * lower bound was specified.\n   * @param value - The value to compare against.\n   */\n  lte(\n    fieldName: IndexFieldName,\n    value: FieldTypeFromFieldPath<Document, IndexFieldName>,\n  ): IndexRange;\n}\n\n/**\n * An expression representing an index range created by\n * {@link IndexRangeBuilder}.\n * @public\n */\nexport abstract class IndexRange {\n  // Property for nominal type support.\n  private _isIndexRange: undefined;\n\n  /**\n   * @internal\n   */\n  constructor() {\n    // only defining the constructor so we can mark it as internal and keep\n    // it out of the docs.\n  }\n}\n", "import { convexT<PERSON><PERSON><PERSON>, JSONValue, Value } from \"../../values/index.js\";\nimport { convexOrUndefinedToJson } from \"../../values/value.js\";\nimport { GenericDocument, GenericIndexFields } from \"../data_model.js\";\nimport {\n  IndexRange,\n  IndexRangeBuilder,\n  LowerBoundIndexRangeBuilder,\n  UpperBoundIndexRangeBuilder,\n} from \"../index_range_builder.js\";\n\nexport type SerializedRangeExpression = {\n  type: \"Eq\" | \"Gt\" | \"Gte\" | \"Lt\" | \"Lte\";\n  fieldPath: string;\n  value: JSONValue;\n};\n\nexport class IndexRangeBuilderImpl\n  extends IndexRange\n  implements\n    IndexRangeBuilder<GenericDocument, GenericIndexFields>,\n    LowerBoundIndexRangeBuilder<GenericDocument, string>,\n    UpperBoundIndexRangeBuilder<GenericDocument, string>\n{\n  private rangeExpressions: ReadonlyArray<SerializedRangeExpression>;\n  private isConsumed: boolean;\n  private constructor(\n    rangeExpressions: ReadonlyArray<SerializedRangeExpression>,\n  ) {\n    super();\n    this.rangeExpressions = rangeExpressions;\n    this.isConsumed = false;\n  }\n\n  static new(): IndexRangeBuilderImpl {\n    return new IndexRangeBuilderImpl([]);\n  }\n\n  private consume() {\n    if (this.isConsumed) {\n      throw new Error(\n        \"IndexRangeBuilder has already been used! Chain your method calls like `q => q.eq(...).eq(...)`. See https://docs.convex.dev/using/indexes\",\n      );\n    }\n    this.isConsumed = true;\n  }\n\n  eq(fieldName: string, value: Value) {\n    this.consume();\n    return new IndexRangeBuilderImpl(\n      this.rangeExpressions.concat({\n        type: \"Eq\",\n        fieldPath: fieldName,\n        value: convexOrUndefinedToJson(value),\n      }),\n    );\n  }\n\n  gt(fieldName: string, value: Value) {\n    this.consume();\n    return new IndexRangeBuilderImpl(\n      this.rangeExpressions.concat({\n        type: \"Gt\",\n        fieldPath: fieldName,\n        value: convexToJson(value),\n      }),\n    );\n  }\n  gte(fieldName: string, value: Value) {\n    this.consume();\n    return new IndexRangeBuilderImpl(\n      this.rangeExpressions.concat({\n        type: \"Gte\",\n        fieldPath: fieldName,\n        value: convexToJson(value),\n      }),\n    );\n  }\n  lt(fieldName: string, value: Value) {\n    this.consume();\n    return new IndexRangeBuilderImpl(\n      this.rangeExpressions.concat({\n        type: \"Lt\",\n        fieldPath: fieldName,\n        value: convexToJson(value),\n      }),\n    );\n  }\n  lte(fieldName: string, value: Value) {\n    this.consume();\n    return new IndexRangeBuilderImpl(\n      this.rangeExpressions.concat({\n        type: \"Lte\",\n        fieldPath: fieldName,\n        value: convexToJson(value),\n      }),\n    );\n  }\n\n  export() {\n    this.consume();\n    return this.rangeExpressions;\n  }\n}\n", "import {\n  FieldType<PERSON><PERSON><PERSON><PERSON>Path,\n  GenericDocument,\n  GenericSearchIndexConfig,\n} from \"./data_model.js\";\n\n/**\n * Builder for defining search filters.\n *\n * A search filter is a chained list of:\n * 1. One search expression constructed with `.search`.\n * 2. Zero or more equality expressions constructed with `.eq`.\n *\n * The search expression must search for text in the index's `searchField`. The\n * filter expressions can use any of the `filterFields` defined in the index.\n *\n * For all other filtering use {@link OrderedQuery.filter}.\n *\n * To learn about full text search, see [Indexes](https://docs.convex.dev/text-search).\n * @public\n */\nexport interface SearchFilterBuilder<\n  Document extends GenericDocument,\n  SearchIndexConfig extends GenericSearchIndexConfig,\n> {\n  /**\n   * Search for the terms in `query` within `doc[fieldName]`.\n   *\n   * This will do a full text search that returns results where any word of of\n   * `query` appears in the field.\n   *\n   * Documents will be returned based on their relevance to the query. This\n   * takes into account:\n   * - How many words in the query appear in the text?\n   * - How many times do they appear?\n   * - How long is the text field?\n   *\n   * @param fieldName - The name of the field to search in. This must be listed\n   * as the index's `searchField`.\n   * @param query - The query text to search for.\n   */\n  search(\n    fieldName: SearchIndexConfig[\"searchField\"],\n    query: string,\n  ): SearchFilterFinalizer<Document, SearchIndexConfig>;\n}\n\n/**\n * Builder to define equality expressions as part of a search filter.\n *\n * See {@link SearchFilterBuilder}.\n *\n * @public\n */\nexport interface SearchFilterFinalizer<\n  Document extends GenericDocument,\n  SearchIndexConfig extends GenericSearchIndexConfig,\n> extends SearchFilter {\n  /**\n   * Restrict this query to documents where `doc[fieldName] === value`.\n   *\n   * @param fieldName - The name of the field to compare. This must be listed in\n   * the search index's `filterFields`.\n   * @param value - The value to compare against.\n   */\n  eq<FieldName extends SearchIndexConfig[\"filterFields\"]>(\n    fieldName: FieldName,\n    value: FieldTypeFromFieldPath<Document, FieldName>,\n  ): SearchFilterFinalizer<Document, SearchIndexConfig>;\n}\n\n/**\n * An expression representing a search filter created by\n * {@link SearchFilterBuilder}.\n *\n * @public\n */\nexport abstract class SearchFilter {\n  // Property for nominal type support.\n  private _isSearchFilter: undefined;\n\n  /**\n   * @internal\n   */\n  constructor() {\n    // only defining the constructor so we can mark it as internal and keep\n    // it out of the docs.\n  }\n}\n", "import { JSONValue, convexOrUndefinedToJson } from \"../../values/value.js\";\nimport {\n  FieldTypeFromFieldPath,\n  GenericDocument,\n  GenericSearchIndexConfig,\n} from \"../data_model.js\";\nimport {\n  SearchFilter,\n  SearchFilterBuilder,\n  SearchFilterFinalizer,\n} from \"../search_filter_builder.js\";\nimport { validateArg } from \"./validate.js\";\n\nexport type SerializedSearchFilter =\n  | {\n      type: \"Search\";\n      fieldPath: string;\n      value: string;\n    }\n  | {\n      type: \"Eq\";\n      fieldPath: string;\n      value: JSONValue;\n    };\n\nexport class SearchFilterBuilderImpl\n  extends SearchFilter\n  implements\n    SearchFilterBuilder<GenericDocument, GenericSearchIndexConfig>,\n    SearchFilterFinalizer<GenericDocument, GenericSearchIndexConfig>\n{\n  private filters: ReadonlyArray<SerializedSearchFilter>;\n  private isConsumed: boolean;\n  private constructor(filters: ReadonlyArray<SerializedSearchFilter>) {\n    super();\n    this.filters = filters;\n    this.isConsumed = false;\n  }\n\n  static new(): SearchFilterBuilderImpl {\n    return new SearchFilterBuilderImpl([]);\n  }\n\n  private consume() {\n    if (this.isConsumed) {\n      throw new Error(\n        \"SearchFilterBuilder has already been used! Chain your method calls like `q => q.search(...).eq(...)`.\",\n      );\n    }\n    this.isConsumed = true;\n  }\n\n  search(\n    fieldName: string,\n    query: string,\n  ): SearchFilterFinalizer<GenericDocument, GenericSearchIndexConfig> {\n    validateArg(fieldName, 1, \"search\", \"fieldName\");\n    validateArg(query, 2, \"search\", \"query\");\n    this.consume();\n    return new SearchFilterBuilderImpl(\n      this.filters.concat({\n        type: \"Search\",\n        fieldPath: fieldName,\n        value: query,\n      }),\n    );\n  }\n  eq<FieldName extends string>(\n    fieldName: FieldName,\n    value: FieldTypeFromFieldPath<GenericDocument, FieldName>,\n  ): SearchFilterFinalizer<GenericDocument, GenericSearchIndexConfig> {\n    validateArg(fieldName, 1, \"eq\", \"fieldName\");\n    // when `undefined` is passed explicitly, it is allowed.\n    if (arguments.length !== 2) {\n      validateArg(value, 2, \"search\", \"value\");\n    }\n    this.consume();\n    return new SearchFilterBuilderImpl(\n      this.filters.concat({\n        type: \"Eq\",\n        fieldPath: fieldName,\n        value: convexOrUndefinedToJson(value),\n      }),\n    );\n  }\n\n  export() {\n    this.consume();\n    return this.filters;\n  }\n}\n", "import { Value, JSONValue, jsonToConvex } from \"../../values/index.js\";\nimport { PaginationResult, PaginationOptions } from \"../pagination.js\";\nimport { performAsyncSyscall, performSyscall } from \"./syscall.js\";\nimport {\n  filterBuilderImpl,\n  serializeExpression,\n} from \"./filter_builder_impl.js\";\nimport { Query, QueryInitializer } from \"../query.js\";\nimport { ExpressionOrValue, FilterBuilder } from \"../filter_builder.js\";\nimport { GenericTableInfo } from \"../data_model.js\";\nimport {\n  IndexRangeBuilderImpl,\n  SerializedRangeExpression,\n} from \"./index_range_builder_impl.js\";\nimport {\n  SearchFilterBuilderImpl,\n  SerializedSearchFilter,\n} from \"./search_filter_builder_impl.js\";\nimport { validateArg, validateArgIsNonNegativeInteger } from \"./validate.js\";\nimport { version } from \"../../index.js\";\n\nconst MAX_QUERY_OPERATORS = 256;\n\ntype QueryOperator = { filter: JSONValue } | { limit: number };\ntype Source =\n  | { type: \"FullTableScan\"; tableName: string; order: \"asc\" | \"desc\" | null }\n  | {\n      type: \"IndexRange\";\n      indexName: string;\n      range: ReadonlyArray<SerializedRangeExpression>;\n      order: \"asc\" | \"desc\" | null;\n    }\n  | {\n      type: \"Search\";\n      indexName: string;\n      filters: ReadonlyArray<SerializedSearchFilter>;\n    };\n\ntype SerializedQuery = {\n  source: Source;\n  operators: Array<QueryOperator>;\n};\n\nexport class QueryInitializerImpl\n  implements QueryInitializer<GenericTableInfo>\n{\n  private tableName: string;\n\n  constructor(tableName: string) {\n    this.tableName = tableName;\n  }\n\n  withIndex(\n    indexName: string,\n    indexRange?: (q: IndexRangeBuilderImpl) => IndexRangeBuilderImpl,\n  ): QueryImpl {\n    validateArg(indexName, 1, \"withIndex\", \"indexName\");\n    let rangeBuilder = IndexRangeBuilderImpl.new();\n    if (indexRange !== undefined) {\n      rangeBuilder = indexRange(rangeBuilder);\n    }\n    return new QueryImpl({\n      source: {\n        type: \"IndexRange\",\n        indexName: this.tableName + \".\" + indexName,\n        range: rangeBuilder.export(),\n        order: null,\n      },\n      operators: [],\n    });\n  }\n\n  withSearchIndex(\n    indexName: string,\n    searchFilter: (q: SearchFilterBuilderImpl) => SearchFilterBuilderImpl,\n  ): QueryImpl {\n    validateArg(indexName, 1, \"withSearchIndex\", \"indexName\");\n    validateArg(searchFilter, 2, \"withSearchIndex\", \"searchFilter\");\n    const searchFilterBuilder = SearchFilterBuilderImpl.new();\n    return new QueryImpl({\n      source: {\n        type: \"Search\",\n        indexName: this.tableName + \".\" + indexName,\n        filters: searchFilter(searchFilterBuilder).export(),\n      },\n      operators: [],\n    });\n  }\n\n  fullTableScan(): QueryImpl {\n    return new QueryImpl({\n      source: {\n        type: \"FullTableScan\",\n        tableName: this.tableName,\n        order: null,\n      },\n      operators: [],\n    });\n  }\n\n  order(order: \"asc\" | \"desc\"): QueryImpl {\n    return this.fullTableScan().order(order);\n  }\n\n  // This is internal API and should not be exposed to developers yet.\n  async count(): Promise<number> {\n    const syscallJSON = await performAsyncSyscall(\"1.0/count\", {\n      table: this.tableName,\n    });\n    const syscallResult = jsonToConvex(syscallJSON) as number;\n    return syscallResult;\n  }\n\n  filter(\n    predicate: (\n      q: FilterBuilder<GenericTableInfo>,\n    ) => ExpressionOrValue<boolean>,\n  ) {\n    return this.fullTableScan().filter(predicate);\n  }\n\n  limit(n: number) {\n    return this.fullTableScan().limit(n);\n  }\n\n  collect(): Promise<any[]> {\n    return this.fullTableScan().collect();\n  }\n\n  take(n: number): Promise<Array<any>> {\n    return this.fullTableScan().take(n);\n  }\n\n  paginate(paginationOpts: PaginationOptions): Promise<PaginationResult<any>> {\n    return this.fullTableScan().paginate(paginationOpts);\n  }\n\n  first(): Promise<any> {\n    return this.fullTableScan().first();\n  }\n\n  unique(): Promise<any> {\n    return this.fullTableScan().unique();\n  }\n\n  [Symbol.asyncIterator](): AsyncIterableIterator<any> {\n    return this.fullTableScan()[Symbol.asyncIterator]();\n  }\n}\n\n/**\n * @param type Whether the query was consumed or closed.\n * @throws An error indicating the query has been closed.\n */\nfunction throwClosedError(type: \"closed\" | \"consumed\"): never {\n  throw new Error(\n    type === \"consumed\"\n      ? \"This query is closed and can't emit any more values.\"\n      : \"This query has been chained with another operator and can't be reused.\",\n  );\n}\n\nexport class QueryImpl implements Query<GenericTableInfo> {\n  private state:\n    | { type: \"preparing\"; query: SerializedQuery }\n    | { type: \"executing\"; queryId: number }\n    | { type: \"closed\" }\n    | { type: \"consumed\" };\n\n  constructor(query: SerializedQuery) {\n    this.state = { type: \"preparing\", query };\n  }\n\n  private takeQuery(): SerializedQuery {\n    if (this.state.type !== \"preparing\") {\n      throw new Error(\n        \"A query can only be chained once and can't be chained after iteration begins.\",\n      );\n    }\n    const query = this.state.query;\n    this.state = { type: \"closed\" };\n    return query;\n  }\n\n  private startQuery(): number {\n    if (this.state.type === \"executing\") {\n      throw new Error(\"Iteration can only begin on a query once.\");\n    }\n    if (this.state.type === \"closed\" || this.state.type === \"consumed\") {\n      throwClosedError(this.state.type);\n    }\n    const query = this.state.query;\n    const { queryId } = performSyscall(\"1.0/queryStream\", { query, version });\n    this.state = { type: \"executing\", queryId };\n    return queryId;\n  }\n\n  private closeQuery() {\n    if (this.state.type === \"executing\") {\n      const queryId = this.state.queryId;\n      performSyscall(\"1.0/queryCleanup\", { queryId });\n    }\n    this.state = { type: \"consumed\" };\n  }\n\n  order(order: \"asc\" | \"desc\"): QueryImpl {\n    validateArg(order, 1, \"order\", \"order\");\n    const query = this.takeQuery();\n    if (query.source.type === \"Search\") {\n      throw new Error(\n        \"Search queries must always be in relevance order. Can not set order manually.\",\n      );\n    }\n    if (query.source.order !== null) {\n      throw new Error(\"Queries may only specify order at most once\");\n    }\n    query.source.order = order;\n    return new QueryImpl(query);\n  }\n\n  filter(\n    predicate: (\n      q: FilterBuilder<GenericTableInfo>,\n    ) => ExpressionOrValue<boolean>,\n  ): any {\n    validateArg(predicate, 1, \"filter\", \"predicate\");\n    const query = this.takeQuery();\n    if (query.operators.length >= MAX_QUERY_OPERATORS) {\n      throw new Error(\n        `Can't construct query with more than ${MAX_QUERY_OPERATORS} operators`,\n      );\n    }\n    query.operators.push({\n      filter: serializeExpression(predicate(filterBuilderImpl)),\n    });\n    return new QueryImpl(query);\n  }\n\n  limit(n: number): any {\n    validateArg(n, 1, \"limit\", \"n\");\n    const query = this.takeQuery();\n    query.operators.push({ limit: n });\n    return new QueryImpl(query);\n  }\n\n  [Symbol.asyncIterator](): AsyncIterableIterator<any> {\n    this.startQuery();\n    return this;\n  }\n\n  async next(): Promise<IteratorResult<any>> {\n    if (this.state.type === \"closed\" || this.state.type === \"consumed\") {\n      throwClosedError(this.state.type);\n    }\n    // Allow calling `.next()` when the query is in \"preparing\" state to implicitly start the\n    // query. This allows the developer to call `.next()` on the query without having to use\n    // a `for await` statement.\n    const queryId =\n      this.state.type === \"preparing\" ? this.startQuery() : this.state.queryId;\n    const { value, done } = await performAsyncSyscall(\"1.0/queryStreamNext\", {\n      queryId,\n    });\n    if (done) {\n      this.closeQuery();\n    }\n    const convexValue = jsonToConvex(value);\n    return { value: convexValue, done };\n  }\n\n  return() {\n    this.closeQuery();\n    return Promise.resolve({ done: true, value: undefined });\n  }\n\n  async paginate(\n    paginationOpts: PaginationOptions,\n  ): Promise<PaginationResult<any>> {\n    validateArg(paginationOpts, 1, \"paginate\", \"options\");\n    if (\n      typeof paginationOpts?.numItems !== \"number\" ||\n      paginationOpts.numItems < 0\n    ) {\n      throw new Error(\n        `\\`options.numItems\\` must be a positive number. Received \\`${paginationOpts?.numItems}\\`.`,\n      );\n    }\n    const query = this.takeQuery();\n    const pageSize = paginationOpts.numItems;\n    const cursor = paginationOpts.cursor;\n    const endCursor = paginationOpts?.endCursor ?? null;\n    const maximumRowsRead = paginationOpts.maximumRowsRead ?? null;\n    const { page, isDone, continueCursor, splitCursor, pageStatus } =\n      await performAsyncSyscall(\"1.0/queryPage\", {\n        query,\n        cursor,\n        endCursor,\n        pageSize,\n        maximumRowsRead,\n        maximumBytesRead: paginationOpts.maximumBytesRead,\n        version,\n      });\n    return {\n      page: page.map((json: string) => jsonToConvex(json)),\n      isDone,\n      continueCursor,\n      splitCursor,\n      pageStatus,\n    };\n  }\n\n  async collect(): Promise<Array<any>> {\n    const out: Value[] = [];\n    for await (const item of this) {\n      out.push(item);\n    }\n    return out;\n  }\n\n  async take(n: number): Promise<Array<any>> {\n    validateArg(n, 1, \"take\", \"n\");\n    validateArgIsNonNegativeInteger(n, 1, \"take\", \"n\");\n    return this.limit(n).collect();\n  }\n\n  async first(): Promise<any | null> {\n    const first_array = await this.take(1);\n    return first_array.length === 0 ? null : first_array[0];\n  }\n\n  async unique(): Promise<any | null> {\n    const first_two_array = await this.take(2);\n    if (first_two_array.length === 0) {\n      return null;\n    }\n    if (first_two_array.length === 2) {\n      throw new Error(`unique() query returned more than one result: \n [${first_two_array[0]._id}, ${first_two_array[1]._id}, ...]`);\n    }\n    return first_two_array[0];\n  }\n}\n", "import {\n  convexTo<PERSON>son,\n  GenericId,\n  jsonToConvex,\n  Value,\n} from \"../../values/index.js\";\nimport { performAsyncSyscall, performSyscall } from \"./syscall.js\";\nimport {\n  GenericDatabaseReader,\n  GenericDatabaseReaderWithTable,\n  GenericDatabaseWriter,\n  GenericDatabaseWriterWithTable,\n} from \"../database.js\";\nimport { QueryInitializerImpl } from \"./query_impl.js\";\nimport { GenericDataModel, GenericDocument } from \"../data_model.js\";\nimport { validateArg } from \"./validate.js\";\nimport { version } from \"../../index.js\";\nimport { patchValueToJson } from \"../../values/value.js\";\n\nasync function get(id: GenericId<string>, isSystem: boolean) {\n  validateArg(id, 1, \"get\", \"id\");\n  if (typeof id !== \"string\") {\n    throw new Error(\n      `Invalid argument \\`id\\` for \\`db.get\\`, expected string but got '${typeof id}': ${\n        id as any\n      }`,\n    );\n  }\n  const args = {\n    id: convexToJson(id),\n    isSystem,\n    version,\n  };\n  const syscallJSON = await performAsyncSyscall(\"1.0/get\", args);\n\n  return jsonToConvex(syscallJSON) as GenericDocument;\n}\n\nexport function setupReader(): GenericDatabaseReader<GenericDataModel> {\n  const reader = (\n    isSystem = false,\n  ): GenericDatabaseReader<GenericDataModel> &\n    GenericDatabaseReaderWithTable<GenericDataModel> => {\n    return {\n      get: async (id: GenericId<string>) => {\n        return await get(id, isSystem);\n      },\n      query: (tableName: string) => {\n        return new TableReader(tableName, isSystem).query();\n      },\n      normalizeId: <TableName extends string>(\n        tableName: TableName,\n        id: string,\n      ): GenericId<TableName> | null => {\n        validateArg(tableName, 1, \"normalizeId\", \"tableName\");\n        validateArg(id, 2, \"normalizeId\", \"id\");\n        const accessingSystemTable = tableName.startsWith(\"_\");\n        if (accessingSystemTable !== isSystem) {\n          throw new Error(\n            `${\n              accessingSystemTable ? \"System\" : \"User\"\n            } tables can only be accessed from db.${\n              isSystem ? \"\" : \"system.\"\n            }normalizeId().`,\n          );\n        }\n        const syscallJSON = performSyscall(\"1.0/db/normalizeId\", {\n          table: tableName,\n          idString: id,\n        });\n        const syscallResult = jsonToConvex(syscallJSON) as any;\n        return syscallResult.id;\n      },\n      // We set the system reader on the next line\n      system: null as any,\n      table: (tableName) => {\n        return new TableReader(tableName, isSystem);\n      },\n    };\n  };\n  const { system: _, ...rest } = reader(true);\n  const r = reader();\n  r.system = rest as any;\n  return r;\n}\n\nasync function insert(tableName: string, value: any) {\n  if (tableName.startsWith(\"_\")) {\n    throw new Error(\"System tables (prefixed with `_`) are read-only.\");\n  }\n  validateArg(tableName, 1, \"insert\", \"table\");\n  validateArg(value, 2, \"insert\", \"value\");\n  const syscallJSON = await performAsyncSyscall(\"1.0/insert\", {\n    table: tableName,\n    value: convexToJson(value),\n  });\n  const syscallResult = jsonToConvex(syscallJSON) as any;\n  return syscallResult._id;\n}\n\nasync function patch(id: any, value: any) {\n  validateArg(id, 1, \"patch\", \"id\");\n  validateArg(value, 2, \"patch\", \"value\");\n  await performAsyncSyscall(\"1.0/shallowMerge\", {\n    id: convexToJson(id),\n    value: patchValueToJson(value as Value),\n  });\n}\n\nasync function replace(id: any, value: any) {\n  validateArg(id, 1, \"replace\", \"id\");\n  validateArg(value, 2, \"replace\", \"value\");\n  await performAsyncSyscall(\"1.0/replace\", {\n    id: convexToJson(id),\n    value: convexToJson(value),\n  });\n}\n\nasync function delete_(id: any) {\n  validateArg(id, 1, \"delete\", \"id\");\n  await performAsyncSyscall(\"1.0/remove\", { id: convexToJson(id) });\n}\n\nexport function setupWriter(): GenericDatabaseWriter<GenericDataModel> &\n  GenericDatabaseWriterWithTable<GenericDataModel> {\n  const reader = setupReader();\n  return {\n    get: reader.get,\n    query: reader.query,\n    normalizeId: reader.normalizeId,\n    system: reader.system as any,\n    insert: async (table, value) => {\n      return await insert(table, value);\n    },\n    patch: async (id, value) => {\n      return await patch(id, value);\n    },\n    replace: async (id, value) => {\n      return await replace(id, value);\n    },\n    delete: async (id) => {\n      return await delete_(id);\n    },\n    table: (tableName) => {\n      return new TableWriter(tableName, false);\n    },\n  };\n}\n\nclass TableReader {\n  constructor(\n    protected readonly tableName: string,\n    protected readonly isSystem: boolean,\n  ) {}\n\n  async get(id: GenericId<string>) {\n    return get(id, this.isSystem);\n  }\n\n  query() {\n    const accessingSystemTable = this.tableName.startsWith(\"_\");\n    if (accessingSystemTable !== this.isSystem) {\n      throw new Error(\n        `${\n          accessingSystemTable ? \"System\" : \"User\"\n        } tables can only be accessed from db.${\n          this.isSystem ? \"\" : \"system.\"\n        }query().`,\n      );\n    }\n    return new QueryInitializerImpl(this.tableName);\n  }\n}\n\nclass TableWriter extends TableReader {\n  async insert(value: any) {\n    return insert(this.tableName, value);\n  }\n  async patch(id: any, value: any) {\n    return patch(id, value);\n  }\n  async replace(id: any, value: any) {\n    return replace(id, value);\n  }\n  async delete(id: any) {\n    return delete_(id);\n  }\n}\n", "import { convexToJson, Value } from \"../../values/index.js\";\nimport { version } from \"../../index.js\";\nimport { performAsyncSyscall } from \"./syscall.js\";\nimport { parseArgs } from \"../../common/index.js\";\nimport { SchedulableFunctionReference, Scheduler } from \"../scheduler.js\";\nimport { Id } from \"../../values/value.js\";\nimport { validateArg } from \"./validate.js\";\nimport { getFunctionAddress } from \"../components/paths.js\";\n\nexport function setupMutationScheduler(): Scheduler {\n  return {\n    runAfter: async (\n      delayMs: number,\n      functionReference: SchedulableFunctionReference,\n      args?: Record<string, Value>,\n    ) => {\n      const syscallArgs = runAfterSyscallArgs(delayMs, functionReference, args);\n      return await performAsyncSyscall(\"1.0/schedule\", syscallArgs);\n    },\n    runAt: async (\n      ms_since_epoch_or_date: number | Date,\n      functionReference: SchedulableFunctionReference,\n      args?: Record<string, Value>,\n    ) => {\n      const syscallArgs = runAtSyscallArgs(\n        ms_since_epoch_or_date,\n        functionReference,\n        args,\n      );\n      return await performAsyncSyscall(\"1.0/schedule\", syscallArgs);\n    },\n    cancel: async (id: Id<\"_scheduled_functions\">) => {\n      validateArg(id, 1, \"cancel\", \"id\");\n      const args = { id: convexToJson(id) };\n      await performAsyncSyscall(\"1.0/cancel_job\", args);\n    },\n  };\n}\n\nexport function setupActionScheduler(requestId: string): Scheduler {\n  return {\n    runAfter: async (\n      delayMs: number,\n      functionReference: SchedulableFunctionReference,\n      args?: Record<string, Value>,\n    ) => {\n      const syscallArgs = {\n        requestId,\n        ...runAfterSyscallArgs(delayMs, functionReference, args),\n      };\n      return await performAsyncSyscall(\"1.0/actions/schedule\", syscallArgs);\n    },\n    runAt: async (\n      ms_since_epoch_or_date: number | Date,\n      functionReference: SchedulableFunctionReference,\n      args?: Record<string, Value>,\n    ) => {\n      const syscallArgs = {\n        requestId,\n        ...runAtSyscallArgs(ms_since_epoch_or_date, functionReference, args),\n      };\n      return await performAsyncSyscall(\"1.0/actions/schedule\", syscallArgs);\n    },\n    cancel: async (id: Id<\"_scheduled_functions\">) => {\n      validateArg(id, 1, \"cancel\", \"id\");\n      const syscallArgs = { id: convexToJson(id) };\n      return await performAsyncSyscall(\"1.0/actions/cancel_job\", syscallArgs);\n    },\n  };\n}\n\nfunction runAfterSyscallArgs(\n  delayMs: number,\n  functionReference: SchedulableFunctionReference,\n  args?: Record<string, Value>,\n) {\n  if (typeof delayMs !== \"number\") {\n    throw new Error(\"`delayMs` must be a number\");\n  }\n  if (!isFinite(delayMs)) {\n    throw new Error(\"`delayMs` must be a finite number\");\n  }\n  if (delayMs < 0) {\n    throw new Error(\"`delayMs` must be non-negative\");\n  }\n  const functionArgs = parseArgs(args);\n  const address = getFunctionAddress(functionReference);\n  // Note the syscall expects a unix timestamp, measured in seconds.\n  const ts = (Date.now() + delayMs) / 1000.0;\n  return {\n    ...address,\n    ts,\n    args: convexToJson(functionArgs),\n    version,\n  };\n}\n\nfunction runAtSyscallArgs(\n  ms_since_epoch_or_date: number | Date,\n  functionReference: SchedulableFunctionReference,\n  args?: Record<string, Value>,\n) {\n  let ts;\n  if (ms_since_epoch_or_date instanceof Date) {\n    ts = ms_since_epoch_or_date.valueOf() / 1000.0;\n  } else if (typeof ms_since_epoch_or_date === \"number\") {\n    // The timestamp the developer passes is in milliseconds, while the syscall\n    // accepts seconds since the epoch.\n    ts = ms_since_epoch_or_date / 1000;\n  } else {\n    throw new Error(\"The invoke time must a Date or a timestamp\");\n  }\n  const address = getFunctionAddress(functionReference);\n  const functionArgs = parseArgs(args);\n  return {\n    ...address,\n    ts,\n    args: convexToJson(functionArgs),\n    version,\n  };\n}\n", "import {\n  FileMetadata,\n  StorageActionWriter,\n  FileStorageId,\n  StorageReader,\n  StorageWriter,\n} from \"../storage.js\";\nimport { version } from \"../../index.js\";\nimport { performAsyncSyscall, performJsSyscall } from \"./syscall.js\";\nimport { validateArg } from \"./validate.js\";\n\nexport function setupStorageReader(requestId: string): StorageReader {\n  return {\n    getUrl: async (storageId: FileStorageId) => {\n      validateArg(storageId, 1, \"getUrl\", \"storageId\");\n      return await performAsyncSyscall(\"1.0/storageGetUrl\", {\n        requestId,\n        version,\n        storageId,\n      });\n    },\n    getMetadata: async (storageId: FileStorageId): Promise<FileMetadata> => {\n      return await performAsyncSyscall(\"1.0/storageGetMetadata\", {\n        requestId,\n        version,\n        storageId,\n      });\n    },\n  };\n}\n\nexport function setupStorageWriter(requestId: string): StorageWriter {\n  const reader = setupStorageReader(requestId);\n  return {\n    generateUploadUrl: async () => {\n      return await performAsyncSyscall(\"1.0/storageGenerateUploadUrl\", {\n        requestId,\n        version,\n      });\n    },\n    delete: async (storageId: FileStorageId) => {\n      await performAsyncSyscall(\"1.0/storageDelete\", {\n        requestId,\n        version,\n        storageId,\n      });\n    },\n    getUrl: reader.getUrl,\n    getMetadata: reader.getMetadata,\n  };\n}\n\nexport function setupStorageActionWriter(\n  requestId: string,\n): StorageActionWriter {\n  const writer = setupStorageWriter(requestId);\n  return {\n    ...writer,\n    store: async (blob: Blob, options?: { sha256?: string }) => {\n      return await performJsSyscall(\"storage/storeBlob\", {\n        requestId,\n        version,\n        blob,\n        options,\n      });\n    },\n    get: async (storageId: FileStorageId) => {\n      return await performJsSyscall(\"storage/getBlob\", {\n        requestId,\n        version,\n        storageId,\n      });\n    },\n  };\n}\n", "import {\n  ConvexError,\n  convexTo<PERSON>son,\n  GenericValidator,\n  jsonToConvex,\n  v,\n  Validator,\n  Value,\n} from \"../../values/index.js\";\nimport { GenericDataModel } from \"../data_model.js\";\nimport {\n  ActionBuilder,\n  DefaultFunctionArgs,\n  GenericActionCtx,\n  GenericMutationCtx,\n  GenericQueryCtx,\n  MutationBuilder,\n  PublicHttpAction,\n  QueryBuilder,\n  RegisteredAction,\n  RegisteredMutation,\n  RegisteredQuery,\n} from \"../registration.js\";\nimport { setupActionCalls } from \"./actions_impl.js\";\nimport { setupActionVectorSearch } from \"./vector_search_impl.js\";\nimport { setupAuth } from \"./authentication_impl.js\";\nimport { setupReader, setupWriter } from \"./database_impl.js\";\nimport { QueryImpl, QueryInitializerImpl } from \"./query_impl.js\";\nimport {\n  setupActionScheduler,\n  setupMutationScheduler,\n} from \"./scheduler_impl.js\";\nimport {\n  setupStorageActionWriter,\n  setupStorageReader,\n  setupStorageWriter,\n} from \"./storage_impl.js\";\nimport { parseArgs } from \"../../common/index.js\";\nimport { performAsyncSyscall } from \"./syscall.js\";\nimport { asObjectValidator } from \"../../values/validator.js\";\nimport { getFunctionAddress } from \"../components/paths.js\";\n\nasync function invokeMutation<\n  F extends (ctx: GenericMutationCtx<GenericDataModel>, ...args: any) => any,\n>(func: F, argsStr: string) {\n  // TODO(presley): Change the function signature and propagate the requestId from Rust.\n  // Ok, to mock it out for now, since queries are only running in V8.\n  const requestId = \"\";\n  const args = jsonToConvex(JSON.parse(argsStr));\n  const mutationCtx = {\n    db: setupWriter(),\n    auth: setupAuth(requestId),\n    storage: setupStorageWriter(requestId),\n    scheduler: setupMutationScheduler(),\n\n    runQuery: (reference: any, args?: any) => runUdf(\"query\", reference, args),\n    runMutation: (reference: any, args?: any) =>\n      runUdf(\"mutation\", reference, args),\n  };\n  const result = await invokeFunction(func, mutationCtx, args as any);\n  validateReturnValue(result);\n  return JSON.stringify(convexToJson(result === undefined ? null : result));\n}\n\nexport function validateReturnValue(v: any) {\n  if (v instanceof QueryInitializerImpl || v instanceof QueryImpl) {\n    throw new Error(\n      \"Return value is a Query. Results must be retrieved with `.collect()`, `.take(n), `.unique()`, or `.first()`.\",\n    );\n  }\n}\n\nexport async function invokeFunction<\n  Ctx,\n  Args extends any[],\n  F extends (ctx: Ctx, ...args: Args) => any,\n>(func: F, ctx: Ctx, args: Args) {\n  let result;\n  try {\n    result = await Promise.resolve(func(ctx, ...args));\n  } catch (thrown: unknown) {\n    throw serializeConvexErrorData(thrown);\n  }\n  return result;\n}\n\nfunction dontCallDirectly(\n  funcType: string,\n  handler: (ctx: any, args: any) => any,\n): unknown {\n  return (ctx: any, args: any) => {\n    globalThis.console.warn(\n      \"Convex functions should not directly call other Convex functions. Consider calling a helper function instead. \" +\n        `e.g. \\`export const foo = ${funcType}(...); await foo(ctx);\\` is not supported. ` +\n        \"See https://docs.convex.dev/production/best-practices/#use-helper-functions-to-write-shared-code\",\n    );\n    return handler(ctx, args);\n  };\n}\n\n// Keep in sync with node executor\nfunction serializeConvexErrorData(thrown: unknown) {\n  if (\n    typeof thrown === \"object\" &&\n    thrown !== null &&\n    Symbol.for(\"ConvexError\") in thrown\n  ) {\n    const error = thrown as ConvexError<any>;\n    error.data = JSON.stringify(\n      convexToJson(error.data === undefined ? null : error.data),\n    );\n    (error as any).ConvexErrorSymbol = Symbol.for(\"ConvexError\");\n    return error;\n  } else {\n    return thrown;\n  }\n}\n\n/**\n * Guard against Convex functions accidentally getting included in a browser bundle.\n * Convex functions may include secret logic or credentials that should not be\n * send to untrusted clients (browsers).\n */\nfunction assertNotBrowser() {\n  if (\n    typeof window === \"undefined\" ||\n    !(window as any).__convexAllowFunctionsInBrowser\n  ) {\n    return;\n  }\n  // JSDom doesn't count, developers are allowed to use JSDom in Convex functions.\n  const isRealBrowser =\n    Object.getOwnPropertyDescriptor(globalThis, \"window\")\n      ?.get?.toString()\n      .includes(\"[native code]\") ?? false;\n  if (isRealBrowser) {\n    throw new Error(\"Convex functions should not be imported in the browser.\");\n  }\n}\n\ntype FunctionDefinition =\n  | ((ctx: any, args: DefaultFunctionArgs) => any)\n  | {\n      args?: GenericValidator | Record<string, GenericValidator>;\n      returns?: GenericValidator | Record<string, GenericValidator>;\n      handler: (ctx: any, args: DefaultFunctionArgs) => any;\n    };\n\nfunction exportArgs(functionDefinition: FunctionDefinition) {\n  return () => {\n    let args: GenericValidator = v.any();\n    if (\n      typeof functionDefinition === \"object\" &&\n      functionDefinition.args !== undefined\n    ) {\n      args = asObjectValidator(functionDefinition.args);\n    }\n    return JSON.stringify(args.json);\n  };\n}\n\nfunction exportReturns(functionDefinition: FunctionDefinition) {\n  return () => {\n    let returns: Validator<any, any, any> | undefined;\n    if (\n      typeof functionDefinition === \"object\" &&\n      functionDefinition.returns !== undefined\n    ) {\n      returns = asObjectValidator(functionDefinition.returns);\n    }\n    return JSON.stringify(returns ? returns.json : null);\n  };\n}\n\n/**\n * Define a mutation in this Convex app's public API.\n *\n * This function will be allowed to modify your Convex database and will be accessible from the client.\n *\n * If you're using code generation, use the `mutation` function in\n * `convex/_generated/server.d.ts` which is typed for your data model.\n *\n * @param func - The mutation function. It receives a {@link GenericMutationCtx} as its first argument.\n * @returns The wrapped mutation. Include this as an `export` to name it and make it accessible.\n *\n * @public\n */\nexport const mutationGeneric: MutationBuilder<any, \"public\"> = ((\n  functionDefinition: FunctionDefinition,\n) => {\n  const handler = (\n    typeof functionDefinition === \"function\"\n      ? functionDefinition\n      : functionDefinition.handler\n  ) as (ctx: GenericMutationCtx<any>, args: any) => any;\n  const func = dontCallDirectly(\"mutation\", handler) as RegisteredMutation<\n    \"public\",\n    any,\n    any\n  >;\n\n  assertNotBrowser();\n  func.isMutation = true;\n  func.isPublic = true;\n  func.invokeMutation = (argsStr) => invokeMutation(handler, argsStr);\n  func.exportArgs = exportArgs(functionDefinition);\n  func.exportReturns = exportReturns(functionDefinition);\n  func._handler = handler;\n  return func;\n}) as MutationBuilder<any, \"public\">;\n\n/**\n * Define a mutation that is only accessible from other Convex functions (but not from the client).\n *\n * This function will be allowed to modify your Convex database. It will not be accessible from the client.\n *\n * If you're using code generation, use the `internalMutation` function in\n * `convex/_generated/server.d.ts` which is typed for your data model.\n *\n * @param func - The mutation function. It receives a {@link GenericMutationCtx} as its first argument.\n * @returns The wrapped mutation. Include this as an `export` to name it and make it accessible.\n *\n * @public\n */\nexport const internalMutationGeneric: MutationBuilder<any, \"internal\"> = ((\n  functionDefinition: FunctionDefinition,\n) => {\n  const handler = (\n    typeof functionDefinition === \"function\"\n      ? functionDefinition\n      : functionDefinition.handler\n  ) as (ctx: GenericMutationCtx<any>, args: any) => any;\n  const func = dontCallDirectly(\n    \"internalMutation\",\n    handler,\n  ) as RegisteredMutation<\"internal\", any, any>;\n\n  assertNotBrowser();\n  func.isMutation = true;\n  func.isInternal = true;\n  func.invokeMutation = (argsStr) => invokeMutation(handler, argsStr);\n  func.exportArgs = exportArgs(functionDefinition);\n  func.exportReturns = exportReturns(functionDefinition);\n  func._handler = handler;\n  return func;\n}) as MutationBuilder<any, \"internal\">;\n\nasync function invokeQuery<\n  F extends (ctx: GenericQueryCtx<GenericDataModel>, ...args: any) => any,\n>(func: F, argsStr: string) {\n  // TODO(presley): Change the function signature and propagate the requestId from Rust.\n  // Ok, to mock it out for now, since queries are only running in V8.\n  const requestId = \"\";\n  const args = jsonToConvex(JSON.parse(argsStr));\n  const queryCtx = {\n    db: setupReader(),\n    auth: setupAuth(requestId),\n    storage: setupStorageReader(requestId),\n    runQuery: (reference: any, args?: any) => runUdf(\"query\", reference, args),\n  };\n  const result = await invokeFunction(func, queryCtx, args as any);\n  validateReturnValue(result);\n  return JSON.stringify(convexToJson(result === undefined ? null : result));\n}\n\n/**\n * Define a query in this Convex app's public API.\n *\n * This function will be allowed to read your Convex database and will be accessible from the client.\n *\n * If you're using code generation, use the `query` function in\n * `convex/_generated/server.d.ts` which is typed for your data model.\n *\n * @param func - The query function. It receives a {@link GenericQueryCtx} as its first argument.\n * @returns The wrapped query. Include this as an `export` to name it and make it accessible.\n *\n * @public\n */\nexport const queryGeneric: QueryBuilder<any, \"public\"> = ((\n  functionDefinition: FunctionDefinition,\n) => {\n  const handler = (\n    typeof functionDefinition === \"function\"\n      ? functionDefinition\n      : functionDefinition.handler\n  ) as (ctx: GenericQueryCtx<any>, args: any) => any;\n  const func = dontCallDirectly(\"query\", handler) as RegisteredQuery<\n    \"public\",\n    any,\n    any\n  >;\n\n  assertNotBrowser();\n  func.isQuery = true;\n  func.isPublic = true;\n  func.invokeQuery = (argsStr) => invokeQuery(handler, argsStr);\n  func.exportArgs = exportArgs(functionDefinition);\n  func.exportReturns = exportReturns(functionDefinition);\n  func._handler = handler;\n  return func;\n}) as QueryBuilder<any, \"public\">;\n\n/**\n * Define a query that is only accessible from other Convex functions (but not from the client).\n *\n * This function will be allowed to read from your Convex database. It will not be accessible from the client.\n *\n * If you're using code generation, use the `internalQuery` function in\n * `convex/_generated/server.d.ts` which is typed for your data model.\n *\n * @param func - The query function. It receives a {@link GenericQueryCtx} as its first argument.\n * @returns The wrapped query. Include this as an `export` to name it and make it accessible.\n *\n * @public\n */\nexport const internalQueryGeneric: QueryBuilder<any, \"internal\"> = ((\n  functionDefinition: FunctionDefinition,\n) => {\n  const handler = (\n    typeof functionDefinition === \"function\"\n      ? functionDefinition\n      : functionDefinition.handler\n  ) as (ctx: GenericQueryCtx<any>, args: any) => any;\n  const func = dontCallDirectly(\"internalQuery\", handler) as RegisteredQuery<\n    \"internal\",\n    any,\n    any\n  >;\n\n  assertNotBrowser();\n  func.isQuery = true;\n  func.isInternal = true;\n  func.invokeQuery = (argsStr) => invokeQuery(handler as any, argsStr);\n  func.exportArgs = exportArgs(functionDefinition);\n  func.exportReturns = exportReturns(functionDefinition);\n  func._handler = handler;\n  return func;\n}) as QueryBuilder<any, \"internal\">;\n\nasync function invokeAction<\n  F extends (ctx: GenericActionCtx<GenericDataModel>, ...args: any) => any,\n>(func: F, requestId: string, argsStr: string) {\n  const args = jsonToConvex(JSON.parse(argsStr));\n  const calls = setupActionCalls(requestId);\n  const ctx = {\n    ...calls,\n    auth: setupAuth(requestId),\n    scheduler: setupActionScheduler(requestId),\n    storage: setupStorageActionWriter(requestId),\n    vectorSearch: setupActionVectorSearch(requestId) as any,\n  };\n  const result = await invokeFunction(func, ctx, args as any);\n  return JSON.stringify(convexToJson(result === undefined ? null : result));\n}\n\n/**\n * Define an action in this Convex app's public API.\n *\n * If you're using code generation, use the `action` function in\n * `convex/_generated/server.d.ts` which is typed for your data model.\n *\n * @param func - The function. It receives a {@link GenericActionCtx} as its first argument.\n * @returns The wrapped function. Include this as an `export` to name it and make it accessible.\n *\n * @public\n */\nexport const actionGeneric: ActionBuilder<any, \"public\"> = ((\n  functionDefinition: FunctionDefinition,\n) => {\n  const handler = (\n    typeof functionDefinition === \"function\"\n      ? functionDefinition\n      : functionDefinition.handler\n  ) as (ctx: GenericActionCtx<any>, args: any) => any;\n  const func = dontCallDirectly(\"action\", handler) as RegisteredAction<\n    \"public\",\n    any,\n    any\n  >;\n\n  assertNotBrowser();\n  func.isAction = true;\n  func.isPublic = true;\n  func.invokeAction = (requestId, argsStr) =>\n    invokeAction(handler, requestId, argsStr);\n  func.exportArgs = exportArgs(functionDefinition);\n  func.exportReturns = exportReturns(functionDefinition);\n  func._handler = handler;\n  return func;\n}) as ActionBuilder<any, \"public\">;\n\n/**\n * Define an action that is only accessible from other Convex functions (but not from the client).\n *\n * If you're using code generation, use the `internalAction` function in\n * `convex/_generated/server.d.ts` which is typed for your data model.\n *\n * @param func - The function. It receives a {@link GenericActionCtx} as its first argument.\n * @returns The wrapped function. Include this as an `export` to name it and make it accessible.\n *\n * @public\n */\nexport const internalActionGeneric: ActionBuilder<any, \"internal\"> = ((\n  functionDefinition: FunctionDefinition,\n) => {\n  const handler = (\n    typeof functionDefinition === \"function\"\n      ? functionDefinition\n      : functionDefinition.handler\n  ) as (ctx: GenericActionCtx<any>, args: any) => any;\n  const func = dontCallDirectly(\"internalAction\", handler) as RegisteredAction<\n    \"internal\",\n    any,\n    any\n  >;\n\n  assertNotBrowser();\n  func.isAction = true;\n  func.isInternal = true;\n  func.invokeAction = (requestId, argsStr) =>\n    invokeAction(handler, requestId, argsStr);\n  func.exportArgs = exportArgs(functionDefinition);\n  func.exportReturns = exportReturns(functionDefinition);\n  func._handler = handler;\n  return func;\n}) as ActionBuilder<any, \"internal\">;\n\nasync function invokeHttpAction<\n  F extends (ctx: GenericActionCtx<GenericDataModel>, request: Request) => any,\n>(func: F, request: Request) {\n  // TODO(presley): Change the function signature and propagate the requestId from Rust.\n  // Ok, to mock it out for now, since http endpoints are only running in V8.\n  const requestId = \"\";\n  const calls = setupActionCalls(requestId);\n  const ctx = {\n    ...calls,\n    auth: setupAuth(requestId),\n    storage: setupStorageActionWriter(requestId),\n    scheduler: setupActionScheduler(requestId),\n    vectorSearch: setupActionVectorSearch(requestId) as any,\n  };\n  return await invokeFunction(func, ctx, [request]);\n}\n\n/**\n * Define a Convex HTTP action.\n *\n * @param func - The function. It receives an {@link GenericActionCtx} as its first argument, and a `Request` object\n * as its second.\n * @returns The wrapped function. Route a URL path to this function in `convex/http.js`.\n *\n * @public\n */\nexport const httpActionGeneric = (\n  func: (\n    ctx: GenericActionCtx<GenericDataModel>,\n    request: Request,\n  ) => Promise<Response>,\n): PublicHttpAction => {\n  const q = dontCallDirectly(\"httpAction\", func) as PublicHttpAction;\n  assertNotBrowser();\n  q.isHttp = true;\n  q.invokeHttpAction = (request) => invokeHttpAction(func as any, request);\n  q._handler = func;\n  return q;\n};\n\nasync function runUdf(\n  udfType: \"query\" | \"mutation\",\n  f: any,\n  args?: Record<string, Value>,\n): Promise<any> {\n  const queryArgs = parseArgs(args);\n  const syscallArgs = {\n    udfType,\n    args: convexToJson(queryArgs),\n    ...getFunctionAddress(f),\n  };\n  const result = await performAsyncSyscall(\"1.0/runUdf\", syscallArgs);\n  return jsonToConvex(result);\n}\n", "import { v } from \"../values/validator.js\";\n\n/**\n * An opaque identifier used for paginating a database query.\n *\n * Cursors are returned from {@link OrderedQuery.paginate} and represent the\n * point of the query where the page of results ended.\n *\n * To continue paginating, pass the cursor back into\n * {@link OrderedQuery.paginate} in the {@link PaginationOptions} object to\n * fetch another page of results.\n *\n * Note: Cursors can only be passed to _exactly_ the same database query that\n * they were generated from. You may not reuse a cursor between different\n * database queries.\n *\n * @public\n */\nexport type Cursor = string;\n\n/**\n * The result of paginating using {@link OrderedQuery.paginate}.\n *\n * @public\n */\nexport interface PaginationResult<T> {\n  /**\n   * The page of results.\n   */\n  page: T[];\n\n  /**\n   * Have we reached the end of the results?\n   */\n  isDone: boolean;\n\n  /**\n   * A {@link Cursor} to continue loading more results.\n   */\n  continueCursor: Cursor;\n\n  /**\n   * A {@link Cursor} to split the page into two, so the page from\n   * (cursor, continueCursor] can be replaced by two pages (cursor, splitCursor]\n   * and (splitCursor, continueCursor].\n   */\n  splitCursor?: Cursor | null;\n\n  /**\n   * When a query reads too much data, it may return 'SplitRecommended' to\n   * indicate that the page should be split into two with `splitCursor`.\n   * When a query reads so much data that `page` might be incomplete, its status\n   * becomes 'SplitRequired'.\n   */\n  pageStatus?: \"SplitRecommended\" | \"SplitRequired\" | null;\n}\n\n/**\n * The options passed to {@link OrderedQuery.paginate}.\n *\n * To use this type in [argument validation](https://docs.convex.dev/functions/validation),\n * use the {@link paginationOptsValidator}.\n *\n * @public\n */\nexport interface PaginationOptions {\n  /**\n   * Number of items to load in this page of results.\n   *\n   * Note: This is only an initial value!\n   *\n   * If you are running this paginated query in a reactive query function, you\n   * may receive more or less items than this if items were added to or removed\n   * from the query range.\n   */\n  numItems: number;\n\n  /**\n   * A {@link Cursor} representing the start of this page or `null` to start\n   * at the beginning of the query results.\n   */\n  cursor: Cursor | null;\n\n  /**\n   * A {@link Cursor} representing the end of this page or `null | undefined` to\n   * use `numItems` instead.\n   *\n   * @internal\n   */\n  endCursor?: Cursor | null;\n\n  /**\n   * The maximum number of rows that should be read from the database.\n   *\n   * This option is different from `numItems` in that it controls the number of rows entering a query's\n   * pipeline, where `numItems` controls the number of rows coming out. For example, a `filter`\n   * may disqualify most of the rows coming in, so setting a low `numItems` would not help\n   * bound its execution time. Instead, set a low `maximumRowsRead` to efficiently paginate\n   * through the filter.\n   *\n   * Currently this is not enforced for search queries.\n   *\n   * @internal\n   */\n  maximumRowsRead?: number;\n\n  /**\n   * The maximum number of bytes that should be read from the database.\n   *\n   * As with {@link PaginationOptions.maximumRowsRead}, this affects the number\n   * of rows entering a query's pipeline.\n   *\n   * Once a paginated query hits its bytes read budget, an incomplete page\n   * will be returned.\n   *\n   * Currently this is not enforced for search queries.\n   *\n   * @internal\n   */\n  maximumBytesRead?: number;\n}\n\n/**\n * A {@link values.Validator} for {@link PaginationOptions}.\n *\n * This includes the standard {@link PaginationOptions} properties along with\n * an optional cache-busting `id` property used by {@link react.usePaginatedQuery}.\n *\n * @public\n */\nexport const paginationOptsValidator = v.object({\n  numItems: v.number(),\n  cursor: v.union(v.string(), v.null()),\n  endCursor: v.optional(v.union(v.string(), v.null())),\n  id: v.optional(v.number()),\n  maximumRowsRead: v.optional(v.number()),\n  maximumBytesRead: v.optional(v.number()),\n});\n", "import { getFunctionName, OptionalRestArgs } from \"../server/api.js\";\nimport { parseArgs } from \"../common/index.js\";\nimport { convexTo<PERSON>son, JSONValue, Value } from \"../values/index.js\";\nimport { SchedulableFunctionReference } from \"./scheduler.js\";\n\ntype CronSchedule = {\n  type: \"cron\";\n  cron: string;\n};\n/** @public */\nexport type IntervalSchedule =\n  | { type: \"interval\"; seconds: number }\n  | { type: \"interval\"; minutes: number }\n  | { type: \"interval\"; hours: number };\n/** @public */\nexport type HourlySchedule = {\n  type: \"hourly\";\n  minuteUTC: number;\n};\n/** @public */\nexport type DailySchedule = {\n  type: \"daily\";\n  hourUTC: number;\n  minuteUTC: number;\n};\nconst DAYS_OF_WEEK = [\n  \"sunday\",\n  \"monday\",\n  \"tuesday\",\n  \"wednesday\",\n  \"thursday\",\n  \"friday\",\n  \"saturday\",\n] as const;\ntype DayOfWeek = (typeof DAYS_OF_WEEK)[number];\n/** @public */\nexport type WeeklySchedule = {\n  type: \"weekly\";\n  dayOfWeek: DayOfWeek;\n  hourUTC: number;\n  minuteUTC: number;\n};\n/** @public */\nexport type MonthlySchedule = {\n  type: \"monthly\";\n  day: number;\n  hourUTC: number;\n  minuteUTC: number;\n};\n\n// Duplicating types so docstrings are visible in signatures:\n// `Expand<Omit<MonthlySchedule, \"type\">>` doesn't preserve docstrings.\n// When we get to TypeScript 4.9, `satisfies` would go nicely here.\n\n/** @public */\nexport type Interval =\n  | {\n      /**\n       * Run a job every `seconds` seconds, beginning\n       * when the job is first deployed to Convex.\n       */\n      seconds: number;\n      minutes?: undefined;\n      hours?: undefined;\n    }\n  | {\n      /**\n       * Run a job every `minutes` minutes, beginning\n       * when the job is first deployed to Convex.\n       */\n      minutes: number;\n      seconds?: undefined;\n      hours?: undefined;\n    }\n  | {\n      /**\n       * Run a job every `hours` hours, beginning when\n       * when the job is first deployed to Convex.\n       */\n      hours: number;\n      seconds?: undefined;\n      minutes?: undefined;\n    };\n\n/** @public */\nexport type Hourly = {\n  /**\n   * Minutes past the hour, 0-59.\n   */\n  minuteUTC: number;\n};\n\n/** @public */\nexport type Daily = {\n  /**\n   * 0-23, hour of day. Remember, this is UTC.\n   */\n  hourUTC: number;\n  /**\n   * 0-59, minute of hour. Remember, this is UTC.\n   */\n  minuteUTC: number;\n};\n\n/** @public */\nexport type Monthly = {\n  /**\n   * 1-31, day of month. Days greater that 28 will not run every month.\n   */\n  day: number;\n  /**\n   * 0-23, hour of day. Remember to convert from your own time zone to UTC.\n   */\n  hourUTC: number;\n  /**\n   * 0-59, minute of hour. Remember to convert from your own time zone to UTC.\n   */\n  minuteUTC: number;\n};\n/** @public */\nexport type Weekly = {\n  /**\n   * \"monday\", \"tuesday\", etc.\n   */\n  dayOfWeek: DayOfWeek;\n  /**\n   * 0-23, hour of day. Remember to convert from your own time zone to UTC.\n   */\n  hourUTC: number;\n  /**\n   * 0-59, minute of hour. Remember to convert from your own time zone to UTC.\n   */\n  minuteUTC: number;\n};\n\n/** @public */\nexport type Schedule =\n  | CronSchedule\n  | IntervalSchedule\n  | HourlySchedule\n  | DailySchedule\n  | WeeklySchedule\n  | MonthlySchedule;\n\n/**\n * A schedule to run a Convex mutation or action on.\n * You can schedule Convex functions to run regularly with\n * {@link interval} and exporting it.\n *\n * @public\n **/\nexport interface CronJob {\n  name: string;\n  args: JSONValue;\n  schedule: Schedule;\n}\n\n/**\n * Create a CronJobs object to schedule recurring tasks.\n *\n * ```js\n * // convex/crons.js\n * import { cronJobs } from 'convex/server';\n * import { api } from \"./_generated/api\";\n *\n * const crons = cronJobs();\n * crons.weekly(\n *   \"weekly re-engagement email\",\n *   {\n *     hourUTC: 17, // (9:30am Pacific/10:30am Daylight Savings Pacific)\n *     minuteUTC: 30,\n *   },\n *   api.emails.send\n * )\n * export default crons;\n * ```\n *\n * @public\n */\nexport const cronJobs = () => new Crons();\n\n/**\n * @public\n *\n * This is a cron string. They're complicated!\n */\ntype CronString = string;\n\nfunction validateIntervalNumber(n: number) {\n  if (!Number.isInteger(n) || n <= 0) {\n    throw new Error(\"Interval must be an integer greater than 0\");\n  }\n}\n\nfunction validatedDayOfMonth(n: number) {\n  if (!Number.isInteger(n) || n < 1 || n > 31) {\n    throw new Error(\"Day of month must be an integer from 1 to 31\");\n  }\n  return n;\n}\n\nfunction validatedDayOfWeek(s: string) {\n  if (!DAYS_OF_WEEK.includes(s as DayOfWeek)) {\n    throw new Error('Day of week must be a string like \"monday\".');\n  }\n  return s as DayOfWeek;\n}\n\nfunction validatedHourOfDay(n: number) {\n  if (!Number.isInteger(n) || n < 0 || n > 23) {\n    throw new Error(\"Hour of day must be an integer from 0 to 23\");\n  }\n  return n;\n}\n\nfunction validatedMinuteOfHour(n: number) {\n  if (!Number.isInteger(n) || n < 0 || n > 59) {\n    throw new Error(\"Minute of hour must be an integer from 0 to 59\");\n  }\n  return n;\n}\n\nfunction validatedCronString(s: string) {\n  return s;\n}\n\nfunction validatedCronIdentifier(s: string) {\n  if (!s.match(/^[ -~]*$/)) {\n    throw new Error(\n      `Invalid cron identifier ${s}: use ASCII letters that are not control characters`,\n    );\n  }\n  return s;\n}\n\n/**\n * A class for scheduling cron jobs.\n *\n * To learn more see the documentation at https://docs.convex.dev/scheduling/cron-jobs\n *\n * @public\n */\nexport class Crons {\n  crons: Record<string, CronJob>;\n  isCrons: true;\n  constructor() {\n    this.isCrons = true;\n    this.crons = {};\n  }\n\n  /** @internal */\n  schedule(\n    cronIdentifier: string,\n    schedule: Schedule,\n    functionReference: SchedulableFunctionReference,\n    args?: Record<string, Value>,\n  ) {\n    const cronArgs = parseArgs(args);\n    validatedCronIdentifier(cronIdentifier);\n    if (cronIdentifier in this.crons) {\n      throw new Error(`Cron identifier registered twice: ${cronIdentifier}`);\n    }\n    this.crons[cronIdentifier] = {\n      name: getFunctionName(functionReference),\n      args: [convexToJson(cronArgs)],\n      schedule: schedule,\n    };\n  }\n\n  /**\n   * Schedule a mutation or action to run at some interval.\n   *\n   * ```js\n   * crons.interval(\"Clear presence data\", {seconds: 30}, api.presence.clear);\n   * ```\n   *\n   * @param identifier - A unique name for this scheduled job.\n   * @param schedule - The time between runs for this scheduled job.\n   * @param functionReference - A {@link FunctionReference} for the function\n   * to schedule.\n   * @param args - The arguments to the function.\n   */\n  interval<FuncRef extends SchedulableFunctionReference>(\n    cronIdentifier: string,\n    schedule: Interval,\n    functionReference: FuncRef,\n    ...args: OptionalRestArgs<FuncRef>\n  ) {\n    const s = schedule;\n    const hasSeconds = +(\"seconds\" in s && s.seconds !== undefined);\n    const hasMinutes = +(\"minutes\" in s && s.minutes !== undefined);\n    const hasHours = +(\"hours\" in s && s.hours !== undefined);\n    const total = hasSeconds + hasMinutes + hasHours;\n    if (total !== 1) {\n      throw new Error(\"Must specify one of seconds, minutes, or hours\");\n    }\n    if (hasSeconds) {\n      validateIntervalNumber(schedule.seconds!);\n    } else if (hasMinutes) {\n      validateIntervalNumber(schedule.minutes!);\n    } else if (hasHours) {\n      validateIntervalNumber(schedule.hours!);\n    }\n    this.schedule(\n      cronIdentifier,\n      { ...schedule, type: \"interval\" },\n      functionReference,\n      ...args,\n    );\n  }\n\n  /**\n   * Schedule a mutation or action to run on an hourly basis.\n   *\n   * ```js\n   * crons.hourly(\n   *   \"Reset high scores\",\n   *   {\n   *     minuteUTC: 30,\n   *   },\n   *   api.scores.reset\n   * )\n   * ```\n   *\n   * @param cronIdentifier - A unique name for this scheduled job.\n   * @param schedule - What time (UTC) each day to run this function.\n   * @param functionReference - A {@link FunctionReference} for the function\n   * to schedule.\n   * @param args - The arguments to the function.\n   */\n  hourly<FuncRef extends SchedulableFunctionReference>(\n    cronIdentifier: string,\n    schedule: Hourly,\n    functionReference: FuncRef,\n    ...args: OptionalRestArgs<FuncRef>\n  ) {\n    const minuteUTC = validatedMinuteOfHour(schedule.minuteUTC);\n    this.schedule(\n      cronIdentifier,\n      { minuteUTC, type: \"hourly\" },\n      functionReference,\n      ...args,\n    );\n  }\n\n  /**\n   * Schedule a mutation or action to run on a daily basis.\n   *\n   * ```js\n   * crons.daily(\n   *   \"Reset high scores\",\n   *   {\n   *     hourUTC: 17, // (9:30am Pacific/10:30am Daylight Savings Pacific)\n   *     minuteUTC: 30,\n   *   },\n   *   api.scores.reset\n   * )\n   * ```\n   *\n   * @param cronIdentifier - A unique name for this scheduled job.\n   * @param schedule - What time (UTC) each day to run this function.\n   * @param functionReference - A {@link FunctionReference} for the function\n   * to schedule.\n   * @param args - The arguments to the function.\n   */\n  daily<FuncRef extends SchedulableFunctionReference>(\n    cronIdentifier: string,\n    schedule: Daily,\n    functionReference: FuncRef,\n    ...args: OptionalRestArgs<FuncRef>\n  ) {\n    const hourUTC = validatedHourOfDay(schedule.hourUTC);\n    const minuteUTC = validatedMinuteOfHour(schedule.minuteUTC);\n    this.schedule(\n      cronIdentifier,\n      { hourUTC, minuteUTC, type: \"daily\" },\n      functionReference,\n      ...args,\n    );\n  }\n\n  /**\n   * Schedule a mutation or action to run on a weekly basis.\n   *\n   * ```js\n   * crons.weekly(\n   *   \"Weekly re-engagement email\",\n   *   {\n   *     dayOfWeek: \"Tuesday\",\n   *     hourUTC: 17, // (9:30am Pacific/10:30am Daylight Savings Pacific)\n   *     minuteUTC: 30,\n   *   },\n   *   api.emails.send\n   * )\n   * ```\n   *\n   * @param cronIdentifier - A unique name for this scheduled job.\n   * @param schedule - What day and time (UTC) each week to run this function.\n   * @param functionReference - A {@link FunctionReference} for the function\n   * to schedule.\n   */\n  weekly<FuncRef extends SchedulableFunctionReference>(\n    cronIdentifier: string,\n    schedule: Weekly,\n    functionReference: FuncRef,\n    ...args: OptionalRestArgs<FuncRef>\n  ) {\n    const dayOfWeek = validatedDayOfWeek(schedule.dayOfWeek);\n    const hourUTC = validatedHourOfDay(schedule.hourUTC);\n    const minuteUTC = validatedMinuteOfHour(schedule.minuteUTC);\n    this.schedule(\n      cronIdentifier,\n      { dayOfWeek, hourUTC, minuteUTC, type: \"weekly\" },\n      functionReference,\n      ...args,\n    );\n  }\n\n  /**\n   * Schedule a mutation or action to run on a monthly basis.\n   *\n   * Note that some months have fewer days than others, so e.g. a function\n   * scheduled to run on the 30th will not run in February.\n   *\n   * ```js\n   * crons.monthly(\n   *   \"Bill customers at \",\n   *   {\n   *     hourUTC: 17, // (9:30am Pacific/10:30am Daylight Savings Pacific)\n   *     minuteUTC: 30,\n   *     day: 1,\n   *   },\n   *   api.billing.billCustomers\n   * )\n   * ```\n   *\n   * @param cronIdentifier - A unique name for this scheduled job.\n   * @param schedule - What day and time (UTC) each month to run this function.\n   * @param functionReference - A {@link FunctionReference} for the function\n   * to schedule.\n   * @param args - The arguments to the function.\n   */\n  monthly<FuncRef extends SchedulableFunctionReference>(\n    cronIdentifier: string,\n    schedule: Monthly,\n    functionReference: FuncRef,\n    ...args: OptionalRestArgs<FuncRef>\n  ) {\n    const day = validatedDayOfMonth(schedule.day);\n    const hourUTC = validatedHourOfDay(schedule.hourUTC);\n    const minuteUTC = validatedMinuteOfHour(schedule.minuteUTC);\n    this.schedule(\n      cronIdentifier,\n      { day, hourUTC, minuteUTC, type: \"monthly\" },\n      functionReference,\n      ...args,\n    );\n  }\n\n  /**\n   * Schedule a mutation or action to run on a recurring basis.\n   *\n   * Like the unix command `cron`, Sunday is 0, Monday is 1, etc.\n   *\n   * ```\n   *  ┌─ minute (0 - 59)\n   *  │ ┌─ hour (0 - 23)\n   *  │ │ ┌─ day of the month (1 - 31)\n   *  │ │ │ ┌─ month (1 - 12)\n   *  │ │ │ │ ┌─ day of the week (0 - 6) (Sunday to Saturday)\n   * \"* * * * *\"\n   * ```\n   *\n   * @param cronIdentifier - A unique name for this scheduled job.\n   * @param cron - Cron string like `\"15 7 * * *\"` (Every day at 7:15 UTC)\n   * @param functionReference - A {@link FunctionReference} for the function\n   * to schedule.\n   * @param args - The arguments to the function.\n   */\n  cron<FuncRef extends SchedulableFunctionReference>(\n    cronIdentifier: string,\n    cron: CronString,\n    functionReference: FuncRef,\n    ...args: OptionalRestArgs<FuncRef>\n  ) {\n    const c = validatedCronString(cron);\n    this.schedule(\n      cronIdentifier,\n      { cron: c, type: \"cron\" },\n      functionReference,\n      ...args,\n    );\n  }\n\n  /** @internal */\n  export() {\n    return JSON.stringify(this.crons);\n  }\n}\n", "import { performJsSyscall } from \"./impl/syscall.js\";\nimport { PublicHttpAction } from \"./registration.js\";\n\n// Note: this list is duplicated in the dashboard.\n/**\n * A list of the methods supported by Convex HTTP actions.\n *\n * HEAD is handled by Convex by running GET and stripping the body.\n * CONNECT is not supported and will not be supported.\n * TRACE is not supported and will not be supported.\n *\n * @public\n */\nexport const ROUTABLE_HTTP_METHODS = [\n  \"GET\",\n  \"POST\",\n  \"PUT\",\n  \"DELETE\",\n  \"OPTIONS\",\n  \"PATCH\",\n] as const;\n/**\n * A type representing the methods supported by Convex HTTP actions.\n *\n * HEAD is handled by Convex by running GET and stripping the body.\n * CONNECT is not supported and will not be supported.\n * TRACE is not supported and will not be supported.\n *\n * @public\n */\nexport type RoutableMethod = (typeof ROUTABLE_HTTP_METHODS)[number];\n\nexport function normalizeMethod(\n  method: RoutableMethod | \"HEAD\",\n): RoutableMethod {\n  // This router routes HEAD requests as GETs, letting <PERSON><PERSON><PERSON> strip thee response\n  // bodies are response bodies afterward.\n  if (method === \"HEAD\") return \"GET\";\n  return method;\n}\n\n/**\n * Return a new {@link HttpRouter} object.\n *\n * @public\n */\nexport const httpRouter = () => new HttpRouter();\n\n/**\n * A type representing a route to an HTTP action using an exact request URL path match.\n *\n * Used by {@link HttpRouter} to route requests to HTTP actions.\n *\n * @public\n */\nexport type RouteSpecWithPath = {\n  /**\n   * Exact HTTP request path to route.\n   */\n  path: string;\n  /**\n   * HTTP method (\"GET\", \"POST\", ...) to route.\n   */\n  method: RoutableMethod;\n  /**\n   * The HTTP action to execute.\n   */\n  handler: PublicHttpAction;\n};\n\n/**\n * A type representing a route to an HTTP action using a request URL path prefix match.\n *\n * Used by {@link HttpRouter} to route requests to HTTP actions.\n *\n * @public\n */\nexport type RouteSpecWithPathPrefix = {\n  /**\n   * An HTTP request path prefix to route. Requests with a path starting with this value\n   * will be routed to the HTTP action.\n   */\n  pathPrefix: string;\n  /**\n   * HTTP method (\"GET\", \"POST\", ...) to route.\n   */\n  method: RoutableMethod;\n  /**\n   * The HTTP action to execute.\n   */\n  handler: PublicHttpAction;\n};\n\n/**\n * A type representing a route to an HTTP action.\n *\n * Used by {@link HttpRouter} to route requests to HTTP actions.\n *\n * @public\n */\nexport type RouteSpec = RouteSpecWithPath | RouteSpecWithPathPrefix;\n\n/**\n * HTTP router for specifying the paths and methods of {@link httpActionGeneric}s\n *\n * An example `convex/http.js` file might look like this.\n *\n * ```js\n * import { httpRouter } from \"convex/server\";\n * import { getMessagesByAuthor } from \"./getMessagesByAuthor\";\n * import { httpAction } from \"./_generated/server\";\n *\n * const http = httpRouter();\n *\n * // HTTP actions can be defined inline...\n * http.route({\n *   path: \"/message\",\n *   method: \"POST\",\n *   handler: httpAction(async ({ runMutation }, request) => {\n *     const { author, body } = await request.json();\n *\n *     await runMutation(api.sendMessage.default, { body, author });\n *     return new Response(null, {\n *       status: 200,\n *     });\n *   })\n * });\n *\n * // ...or they can be imported from other files.\n * http.route({\n *   path: \"/getMessagesByAuthor\",\n *   method: \"GET\",\n *   handler: getMessagesByAuthor,\n * });\n *\n * // Convex expects the router to be the default export of `convex/http.js`.\n * export default http;\n * ```\n *\n * @public\n */\nexport class HttpRouter {\n  exactRoutes: Map<string, Map<RoutableMethod, PublicHttpAction>> = new Map();\n  prefixRoutes: Map<RoutableMethod, Map<string, PublicHttpAction>> = new Map();\n  isRouter: true = true;\n\n  /**\n   * Specify an HttpAction to be used to respond to requests\n   * for an HTTP method (e.g. \"GET\") and a path or pathPrefix.\n   *\n   * Paths must begin with a slash. Path prefixes must also end in a slash.\n   *\n   * ```js\n   * // matches `/profile` (but not `/profile/`)\n   * http.route({ path: \"/profile\", method: \"GET\", handler: getProfile})\n   *\n   * // matches `/profiles/`, `/profiles/abc`, and `/profiles/a/c/b` (but not `/profile`)\n   * http.route({ pathPrefix: \"/profile/\", method: \"GET\", handler: getProfile})\n   * ```\n   */\n  route = (spec: RouteSpec) => {\n    if (!spec.handler) throw new Error(`route requires handler`);\n    if (!spec.method) throw new Error(`route requires method`);\n    const { method, handler } = spec;\n    if (!ROUTABLE_HTTP_METHODS.includes(method)) {\n      throw new Error(\n        `'${method}' is not an allowed HTTP method (like GET, POST, PUT etc.)`,\n      );\n    }\n\n    if (\"path\" in spec) {\n      if (\"pathPrefix\" in spec) {\n        throw new Error(\n          `Invalid httpRouter route: cannot contain both 'path' and 'pathPrefix'`,\n        );\n      }\n      if (!spec.path.startsWith(\"/\")) {\n        throw new Error(`path '${spec.path}' does not start with a /`);\n      }\n      const methods: Map<RoutableMethod, PublicHttpAction> =\n        this.exactRoutes.has(spec.path)\n          ? this.exactRoutes.get(spec.path)!\n          : new Map();\n      if (methods.has(method)) {\n        throw new Error(\n          `Path '${spec.path}' for method ${method} already in use`,\n        );\n      }\n      methods.set(method, handler);\n      this.exactRoutes.set(spec.path, methods);\n    } else if (\"pathPrefix\" in spec) {\n      if (!spec.pathPrefix.startsWith(\"/\")) {\n        throw new Error(\n          `pathPrefix '${spec.pathPrefix}' does not start with a /`,\n        );\n      }\n      if (!spec.pathPrefix.endsWith(\"/\")) {\n        throw new Error(`pathPrefix ${spec.pathPrefix} must end with a /`);\n      }\n      const prefixes =\n        this.prefixRoutes.get(method) || new Map<string, PublicHttpAction>();\n      if (prefixes.has(spec.pathPrefix)) {\n        throw new Error(\n          `${spec.method} pathPrefix ${spec.pathPrefix} is already defined`,\n        );\n      }\n      prefixes.set(spec.pathPrefix, handler);\n      this.prefixRoutes.set(method, prefixes);\n    } else {\n      throw new Error(\n        `Invalid httpRouter route entry: must contain either field 'path' or 'pathPrefix'`,\n      );\n    }\n  };\n\n  /**\n   * Returns a list of routed HTTP actions.\n   *\n   * These are used to populate the list of routes shown in the Functions page of the Convex dashboard.\n   *\n   * @returns - an array of [path, method, endpoint] tuples.\n   */\n  getRoutes = (): Array<\n    Readonly<[string, RoutableMethod, PublicHttpAction]>\n  > => {\n    const exactPaths: string[] = [...this.exactRoutes.keys()].sort();\n    const exact = exactPaths.flatMap((path) =>\n      [...this.exactRoutes.get(path)!.keys()]\n        .sort()\n        .map(\n          (method) =>\n            [path, method, this.exactRoutes.get(path)!.get(method)!] as const,\n        ),\n    );\n\n    const prefixPathMethods = [...this.prefixRoutes.keys()].sort();\n    const prefixes = prefixPathMethods.flatMap((method) =>\n      [...this.prefixRoutes.get(method)!.keys()]\n        .sort()\n        .map(\n          (pathPrefix) =>\n            [\n              `${pathPrefix}*`,\n              method,\n              this.prefixRoutes.get(method)!.get(pathPrefix)!,\n            ] as const,\n        ),\n    );\n\n    return [...exact, ...prefixes];\n  };\n\n  /**\n   * Returns the appropriate HTTP action and its routed request path and method.\n   *\n   * The path and method returned are used for logging and metrics, and should\n   * match up with one of the routes returned by `getRoutes`.\n   *\n   * For example,\n   *\n   * ```js\n   * http.route({ pathPrefix: \"/profile/\", method: \"GET\", handler: getProfile});\n   *\n   * http.lookup(\"/profile/abc\", \"GET\") // returns [getProfile, \"GET\", \"/profile/*\"]\n   *```\n   *\n   * @returns - a tuple [{@link PublicHttpAction}, method, path] or null.\n   */\n  lookup = (\n    path: string,\n    method: RoutableMethod | \"HEAD\",\n  ): Readonly<[PublicHttpAction, RoutableMethod, string]> | null => {\n    method = normalizeMethod(method);\n    const exactMatch = this.exactRoutes.get(path)?.get(method);\n    if (exactMatch) return [exactMatch, method, path];\n\n    const prefixes = this.prefixRoutes.get(method) || new Map();\n    const prefixesSorted = [...prefixes.entries()].sort(\n      ([prefixA, _a], [prefixB, _b]) => prefixB.length - prefixA.length,\n    );\n    for (const [pathPrefix, endpoint] of prefixesSorted) {\n      if (path.startsWith(pathPrefix)) {\n        return [endpoint, method, `${pathPrefix}*`];\n      }\n    }\n    return null;\n  };\n\n  /**\n   * Given a JSON string representation of a Request object, return a Response\n   * by routing the request and running the appropriate endpoint or returning\n   * a 404 Response.\n   *\n   * @param argsStr - a JSON string representing a Request object.\n   *\n   * @returns - a Response object.\n   */\n  runRequest = async (\n    argsStr: string,\n    requestRoute: string,\n  ): Promise<string> => {\n    const request = performJsSyscall(\"requestFromConvexJson\", {\n      convexJson: JSON.parse(argsStr),\n    });\n\n    let pathname = requestRoute;\n    if (!pathname || typeof pathname !== \"string\") {\n      pathname = new URL(request.url).pathname;\n    }\n\n    const method = request.method;\n    const match = this.lookup(pathname, method as RoutableMethod);\n    if (!match) {\n      const response = new Response(`No HttpAction routed for ${pathname}`, {\n        status: 404,\n      });\n      return JSON.stringify(\n        performJsSyscall(\"convexJsonFromResponse\", { response }),\n      );\n    }\n    const [endpoint, _method, _path] = match;\n    const response = await endpoint.invokeHttpAction(request);\n    return JSON.stringify(\n      performJsSyscall(\"convexJsonFromResponse\", { response }),\n    );\n  };\n}\n", "import { PropertyValidators, convexToJson } from \"../../values/index.js\";\nimport { version } from \"../../index.js\";\nimport {\n  AnyFunctionReference,\n  FunctionReference,\n  FunctionType,\n} from \"../api.js\";\nimport { performAsyncSyscall } from \"../impl/syscall.js\";\nimport { DefaultFunctionArgs } from \"../registration.js\";\nimport {\n  AppDefinitionAnalysis,\n  ComponentDefinitionAnalysis,\n  ComponentDefinitionType,\n} from \"./definition.js\";\nimport {\n  getFunctionAddress,\n  setReferencePath,\n  toReferencePath,\n} from \"./paths.js\";\nexport { getFunctionAddress } from \"./paths.js\";\n\n/**\n * A serializable reference to a Convex function.\n * Passing a this reference to another component allows that component to call this\n * function during the current function execution or at any later time.\n * Function handles are used like `api.folder.function` FunctionReferences,\n * e.g. `ctx.scheduler.runAfter(0, functionReference, args)`.\n *\n * A function reference is stable across code pushes but it's possible\n * the Convex function it refers to might no longer exist.\n *\n * This is a feature of components, which are in beta.\n * This API is unstable and may change in subsequent releases.\n */\nexport type FunctionHandle<\n  Type extends FunctionType,\n  Args extends DefaultFunctionArgs = any,\n  ReturnType = any,\n> = string & FunctionReference<Type, \"internal\", Args, ReturnType>;\n\n/**\n * Create a serializable reference to a Convex function.\n * Passing a this reference to another component allows that component to call this\n * function during the current function execution or at any later time.\n * Function handles are used like `api.folder.function` FunctionReferences,\n * e.g. `ctx.scheduler.runAfter(0, functionReference, args)`.\n *\n * A function reference is stable across code pushes but it's possible\n * the Convex function it refers to might no longer exist.\n *\n * This is a feature of components, which are in beta.\n * This API is unstable and may change in subsequent releases.\n */\nexport async function createFunctionHandle<\n  Type extends FunctionType,\n  Args extends DefaultFunctionArgs,\n  ReturnType,\n>(\n  functionReference: FunctionReference<\n    Type,\n    \"public\" | \"internal\",\n    Args,\n    ReturnType\n  >,\n): Promise<FunctionHandle<Type, Args, ReturnType>> {\n  const address = getFunctionAddress(functionReference);\n  return await performAsyncSyscall(\"1.0/createFunctionHandle\", {\n    ...address,\n    version,\n  });\n}\n\ninterface ComponentExports {\n  [key: string]: FunctionReference<any, any, any, any> | ComponentExports;\n}\n\n/**\n * An object of this type should be the default export of a\n * convex.config.ts file in a component definition directory.\n *\n * This is a feature of components, which are in beta.\n * This API is unstable and may change in subsequent releases.\n */\nexport type ComponentDefinition<Exports extends ComponentExports = any> = {\n  /**\n   * Install a component with the given definition in this component definition.\n   *\n   * Takes a component definition and an optional name.\n   *\n   * For editor tooling this method expects a {@link ComponentDefinition}\n   * but at runtime the object that is imported will be a {@link ImportedComponentDefinition}\n   */\n  use<Definition extends ComponentDefinition<any>>(\n    definition: Definition,\n    options?: {\n      name?: string;\n    },\n  ): InstalledComponent<Definition>;\n\n  /**\n   * Internal type-only property tracking exports provided.\n   *\n   * @deprecated This is a type-only property, don't use it.\n   */\n  __exports: Exports;\n};\n\ntype ComponentDefinitionExports<T extends ComponentDefinition<any>> =\n  T[\"__exports\"];\n\n/**\n * An object of this type should be the default export of a\n * convex.config.ts file in a component-aware convex directory.\n *\n * This is a feature of components, which are in beta.\n * This API is unstable and may change in subsequent releases.\n */\nexport type AppDefinition = {\n  /**\n   * Install a component with the given definition in this component definition.\n   *\n   * Takes a component definition and an optional name.\n   *\n   * For editor tooling this method expects a {@link ComponentDefinition}\n   * but at runtime the object that is imported will be a {@link ImportedComponentDefinition}\n   */\n  use<Definition extends ComponentDefinition<any>>(\n    definition: Definition,\n    options?: {\n      name?: string;\n    },\n  ): InstalledComponent<Definition>;\n};\n\ninterface ExportTree {\n  // Tree with serialized `Reference`s as leaves.\n  [key: string]: string | ExportTree;\n}\n\ntype CommonDefinitionData = {\n  _isRoot: boolean;\n  _childComponents: [\n    string,\n    ImportedComponentDefinition,\n    Record<string, any> | null,\n  ][];\n  _exportTree: ExportTree;\n};\n\ntype ComponentDefinitionData = CommonDefinitionData & {\n  _args: PropertyValidators;\n  _name: string;\n  _onInitCallbacks: Record<string, (argsStr: string) => string>;\n};\ntype AppDefinitionData = CommonDefinitionData;\n\n/**\n * Used to refer to an already-installed component.\n */\nclass InstalledComponent<Definition extends ComponentDefinition<any>> {\n  /**\n   * @internal\n   */\n  _definition: Definition;\n\n  /**\n   * @internal\n   */\n  _name: string;\n\n  constructor(definition: Definition, name: string) {\n    this._definition = definition;\n    this._name = name;\n    setReferencePath(this, `_reference/childComponent/${name}`);\n  }\n\n  get exports(): ComponentDefinitionExports<Definition> {\n    return createExports(this._name, []);\n  }\n}\n\nfunction createExports(name: string, pathParts: string[]): any {\n  const handler: ProxyHandler<any> = {\n    get(_, prop: string | symbol) {\n      if (typeof prop === \"string\") {\n        const newParts = [...pathParts, prop];\n        return createExports(name, newParts);\n      } else if (prop === toReferencePath) {\n        let reference = `_reference/childComponent/${name}`;\n        for (const part of pathParts) {\n          reference += `/${part}`;\n        }\n        return reference;\n      } else {\n        return undefined;\n      }\n    },\n  };\n  return new Proxy({}, handler);\n}\n\nfunction use<Definition extends ComponentDefinition<any>>(\n  this: CommonDefinitionData,\n  definition: Definition,\n  options?: {\n    name?: string;\n  },\n): InstalledComponent<Definition> {\n  // At runtime an imported component will have this shape.\n  const importedComponentDefinition =\n    definition as unknown as ImportedComponentDefinition;\n  if (typeof importedComponentDefinition.componentDefinitionPath !== \"string\") {\n    throw new Error(\n      \"Component definition does not have the required componentDefinitionPath property. This code only works in Convex runtime.\",\n    );\n  }\n  const name =\n    options?.name ||\n    // added recently\n    importedComponentDefinition.defaultName ||\n    // can be removed once backend is out\n    importedComponentDefinition.componentDefinitionPath.split(\"/\").pop()!;\n  this._childComponents.push([name, importedComponentDefinition, {}]);\n  return new InstalledComponent(definition, name);\n}\n\n/**\n * The runtime type of a ComponentDefinition. TypeScript will claim\n * the default export of a module like \"cool-component/convex.config.js\"\n * is a `@link ComponentDefinition}, but during component definition evaluation\n * this is its type instead.\n *\n * This is a feature of components, which are in beta.\n * This API is unstable and may change in subsequent releases.\n */\nexport type ImportedComponentDefinition = {\n  componentDefinitionPath: string;\n  defaultName: string;\n};\n\nfunction exportAppForAnalysis(\n  this: ComponentDefinition<any> & AppDefinitionData,\n): AppDefinitionAnalysis {\n  const definitionType = { type: \"app\" as const };\n  const childComponents = serializeChildComponents(this._childComponents);\n  return {\n    definitionType,\n    childComponents: childComponents as any,\n    httpMounts: {},\n    exports: serializeExportTree(this._exportTree),\n  };\n}\n\nfunction serializeExportTree(tree: ExportTree): any {\n  const branch: any[] = [];\n  for (const [key, child] of Object.entries(tree)) {\n    let node;\n    if (typeof child === \"string\") {\n      node = { type: \"leaf\", leaf: child };\n    } else {\n      node = serializeExportTree(child);\n    }\n    branch.push([key, node]);\n  }\n  return { type: \"branch\", branch };\n}\n\nfunction serializeChildComponents(\n  childComponents: [\n    string,\n    ImportedComponentDefinition,\n    Record<string, any> | null,\n  ][],\n): {\n  name: string;\n  path: string;\n  args: [string, { type: \"value\"; value: string }][] | null;\n}[] {\n  return childComponents.map(([name, definition, p]) => {\n    let args: [string, { type: \"value\"; value: string }][] | null = null;\n    if (p !== null) {\n      args = [];\n      for (const [name, value] of Object.entries(p)) {\n        if (value !== undefined) {\n          args.push([\n            name,\n            { type: \"value\", value: JSON.stringify(convexToJson(value)) },\n          ]);\n        }\n      }\n    }\n    // we know that components carry this extra information\n    const path = definition.componentDefinitionPath;\n    if (!path)\n      throw new Error(\n        \"no .componentPath for component definition \" +\n          JSON.stringify(definition, null, 2),\n      );\n\n    return {\n      name: name!,\n      path: path!,\n      args,\n    };\n  });\n}\n\nfunction exportComponentForAnalysis(\n  this: ComponentDefinition<any> & ComponentDefinitionData,\n): ComponentDefinitionAnalysis {\n  const args: [string, { type: \"value\"; value: string }][] = Object.entries(\n    this._args,\n  ).map(([name, validator]) => [\n    name,\n    {\n      type: \"value\",\n      value: JSON.stringify(validator.json),\n    },\n  ]);\n  const definitionType: ComponentDefinitionType = {\n    type: \"childComponent\" as const,\n    name: this._name,\n    args,\n  };\n  const childComponents = serializeChildComponents(this._childComponents);\n  return {\n    name: this._name,\n    definitionType,\n    childComponents: childComponents as any,\n    httpMounts: {},\n    exports: serializeExportTree(this._exportTree),\n  };\n}\n\n// This is what is actually contained in a ComponentDefinition.\ntype RuntimeComponentDefinition = Omit<ComponentDefinition<any>, \"__exports\"> &\n  ComponentDefinitionData & {\n    export: () => ComponentDefinitionAnalysis;\n  };\ntype RuntimeAppDefinition = AppDefinition &\n  AppDefinitionData & {\n    export: () => AppDefinitionAnalysis;\n  };\n\n/**\n * Define a component, a piece of a Convex deployment with namespaced resources.\n *\n * The default\n * the default export of a module like \"cool-component/convex.config.js\"\n * is a `@link ComponentDefinition}, but during component definition evaluation\n * this is its type instead.\n *\n * @param name Name must be alphanumeric plus underscores. Typically these are\n * lowercase with underscores like `\"onboarding_flow_tracker\"`.\n *\n * This is a feature of components, which are in beta.\n * This API is unstable and may change in subsequent releases.\n */\nexport function defineComponent<Exports extends ComponentExports = any>(\n  name: string,\n): ComponentDefinition<Exports> {\n  const ret: RuntimeComponentDefinition = {\n    _isRoot: false,\n    _name: name,\n    _args: {},\n    _childComponents: [],\n    _exportTree: {},\n    _onInitCallbacks: {},\n\n    export: exportComponentForAnalysis,\n    use,\n\n    // pretend to conform to ComponentDefinition, which temporarily expects __args\n    ...({} as { __args: any; __exports: any }),\n  };\n  return ret as any as ComponentDefinition<Exports>;\n}\n\n/**\n * Attach components, reuseable pieces of a Convex deployment, to this Convex app.\n *\n * This is a feature of components, which are in beta.\n * This API is unstable and may change in subsequent releases.\n */\nexport function defineApp(): AppDefinition {\n  const ret: RuntimeAppDefinition = {\n    _isRoot: true,\n    _childComponents: [],\n    _exportTree: {},\n\n    export: exportAppForAnalysis,\n    use,\n  };\n  return ret as AppDefinition;\n}\n\ntype AnyInterfaceType = {\n  [key: string]: AnyInterfaceType;\n} & AnyFunctionReference;\nexport type AnyComponentReference = Record<string, AnyInterfaceType>;\n\nexport type AnyChildComponents = Record<string, AnyComponentReference>;\n\n/**\n * @internal\n */\nexport function currentSystemUdfInComponent(\n  componentId: string,\n): AnyComponentReference {\n  return {\n    [toReferencePath]: `_reference/currentSystemUdfInComponent/${componentId}`,\n  };\n}\n\nfunction createChildComponents(\n  root: string,\n  pathParts: string[],\n): AnyChildComponents {\n  const handler: ProxyHandler<object> = {\n    get(_, prop: string | symbol) {\n      if (typeof prop === \"string\") {\n        const newParts = [...pathParts, prop];\n        return createChildComponents(root, newParts);\n      } else if (prop === toReferencePath) {\n        if (pathParts.length < 1) {\n          const found = [root, ...pathParts].join(\".\");\n          throw new Error(\n            `API path is expected to be of the form \\`${root}.childComponent.functionName\\`. Found: \\`${found}\\``,\n          );\n        }\n        return `_reference/childComponent/` + pathParts.join(\"/\");\n      } else {\n        return undefined;\n      }\n    },\n  };\n  return new Proxy({}, handler);\n}\n\nexport const componentsGeneric = () => createChildComponents(\"components\", []);\n\nexport type AnyComponents = AnyChildComponents;\n", "/**\n * Utilities for defining the schema of your Convex project.\n *\n * ## Usage\n *\n * Schemas should be placed in a `schema.ts` file in your `convex/` directory.\n *\n * Schema definitions should be built using {@link defineSchema},\n * {@link defineTable}, and {@link values.v}. Make sure to export the schema as the\n * default export.\n *\n * ```ts\n * import { defineSchema, defineTable } from \"convex/server\";\n * import { v } from \"convex/values\";\n *\n *  export default defineSchema({\n *    messages: defineTable({\n *      body: v.string(),\n *      user: v.id(\"users\"),\n *    }),\n *    users: defineTable({\n *      name: v.string(),\n *    }),\n *  });\n * ```\n *\n * To learn more about schemas, see [Defining a Schema](https://docs.convex.dev/using/schemas).\n * @module\n */\nimport {\n  AnyDataModel,\n  GenericDataModel,\n  GenericTableIndexes,\n  GenericTableSearchIndexes,\n  GenericTableVectorIndexes,\n  TableNamesInDataModel,\n} from \"../server/data_model.js\";\nimport {\n  <PERSON><PERSON><PERSON><PERSON>,\n  IndexTiebreakerField,\n  SystemFields,\n  SystemIndexes,\n} from \"../server/system_fields.js\";\nimport { Expand } from \"../type_utils.js\";\nimport {\n  GenericValidator,\n  ObjectType,\n  isValidator,\n  v,\n} from \"../values/validator.js\";\nimport { VObject, Validator } from \"../values/validators.js\";\n\n/**\n * Extract all of the index field paths within a {@link Validator}.\n *\n * This is used within {@link defineTable}.\n * @public\n */\ntype ExtractFieldPaths<T extends Validator<any, any, any>> =\n  // Add in the system fields available in index definitions.\n  // This should be everything except for `_id` because thats added to indexes\n  // automatically.\n  T[\"fieldPaths\"] | keyof SystemFields;\n\n/**\n * Extract the {@link GenericDocument} within a {@link Validator} and\n * add on the system fields.\n *\n * This is used within {@link defineTable}.\n * @public\n */\ntype ExtractDocument<T extends Validator<any, any, any>> =\n  // Add the system fields to `Value` (except `_id` because it depends on\n  //the table name) and trick TypeScript into expanding them.\n  Expand<SystemFields & T[\"type\"]>;\n\n/**\n * The configuration for a full text search index.\n *\n * @public\n */\nexport interface SearchIndexConfig<\n  SearchField extends string,\n  FilterFields extends string,\n> {\n  /**\n   * The field to index for full text search.\n   *\n   * This must be a field of type `string`.\n   */\n  searchField: SearchField;\n\n  /**\n   * Additional fields to index for fast filtering when running search queries.\n   */\n  filterFields?: FilterFields[];\n}\n\n/**\n * The configuration for a vector index.\n *\n * @public\n */\nexport interface VectorIndexConfig<\n  VectorField extends string,\n  FilterFields extends string,\n> {\n  /**\n   * The field to index for vector search.\n   *\n   * This must be a field of type `v.array(v.float64())` (or a union)\n   */\n  vectorField: VectorField;\n  /**\n   * The length of the vectors indexed. This must be between 2 and 2048 inclusive.\n   */\n  dimensions: number;\n  /**\n   * Additional fields to index for fast filtering when running vector searches.\n   */\n  filterFields?: FilterFields[];\n}\n\n/**\n * @internal\n */\nexport type VectorIndex = {\n  indexDescriptor: string;\n  vectorField: string;\n  dimensions: number;\n  filterFields: string[];\n};\n\n/**\n * @internal\n */\nexport type Index = {\n  indexDescriptor: string;\n  fields: string[];\n};\n\n/**\n * @internal\n */\nexport type SearchIndex = {\n  indexDescriptor: string;\n  searchField: string;\n  filterFields: string[];\n};\n/**\n * The definition of a table within a schema.\n *\n * This should be produced by using {@link defineTable}.\n * @public\n */\nexport class TableDefinition<\n  DocumentType extends Validator<any, any, any> = Validator<any, any, any>,\n  Indexes extends GenericTableIndexes = {},\n  SearchIndexes extends GenericTableSearchIndexes = {},\n  VectorIndexes extends GenericTableVectorIndexes = {},\n> {\n  private indexes: Index[];\n  private searchIndexes: SearchIndex[];\n  private vectorIndexes: VectorIndex[];\n  // The type of documents stored in this table.\n  validator: DocumentType;\n\n  /**\n   * @internal\n   */\n  constructor(documentType: DocumentType) {\n    this.indexes = [];\n    this.searchIndexes = [];\n    this.vectorIndexes = [];\n    this.validator = documentType;\n  }\n\n  /**\n   * Define an index on this table.\n   *\n   * To learn about indexes, see [Defining Indexes](https://docs.convex.dev/using/indexes).\n   *\n   * @param name - The name of the index.\n   * @param fields - The fields to index, in order. Must specify at least one\n   * field.\n   * @returns A {@link TableDefinition} with this index included.\n   */\n  index<\n    IndexName extends string,\n    FirstFieldPath extends ExtractFieldPaths<DocumentType>,\n    RestFieldPaths extends ExtractFieldPaths<DocumentType>[],\n  >(\n    name: IndexName,\n    fields: [FirstFieldPath, ...RestFieldPaths],\n  ): TableDefinition<\n    DocumentType,\n    // Update `Indexes` to include the new index and use `Expand` to make the\n    // types look pretty in editors.\n    Expand<\n      Indexes &\n        Record<\n          IndexName,\n          [FirstFieldPath, ...RestFieldPaths, IndexTiebreakerField]\n        >\n    >,\n    SearchIndexes,\n    VectorIndexes\n  > {\n    this.indexes.push({ indexDescriptor: name, fields });\n    return this;\n  }\n\n  /**\n   * Define a search index on this table.\n   *\n   * To learn about search indexes, see [Search](https://docs.convex.dev/text-search).\n   *\n   * @param name - The name of the index.\n   * @param indexConfig - The search index configuration object.\n   * @returns A {@link TableDefinition} with this search index included.\n   */\n  searchIndex<\n    IndexName extends string,\n    SearchField extends ExtractFieldPaths<DocumentType>,\n    FilterFields extends ExtractFieldPaths<DocumentType> = never,\n  >(\n    name: IndexName,\n    indexConfig: Expand<SearchIndexConfig<SearchField, FilterFields>>,\n  ): TableDefinition<\n    DocumentType,\n    Indexes,\n    // Update `SearchIndexes` to include the new index and use `Expand` to make\n    // the types look pretty in editors.\n    Expand<\n      SearchIndexes &\n        Record<\n          IndexName,\n          {\n            searchField: SearchField;\n            filterFields: FilterFields;\n          }\n        >\n    >,\n    VectorIndexes\n  > {\n    this.searchIndexes.push({\n      indexDescriptor: name,\n      searchField: indexConfig.searchField,\n      filterFields: indexConfig.filterFields || [],\n    });\n    return this;\n  }\n\n  /**\n   * Define a vector index on this table.\n   *\n   * To learn about vector indexes, see [Vector Search](https://docs.convex.dev/vector-search).\n   *\n   * @param name - The name of the index.\n   * @param indexConfig - The vector index configuration object.\n   * @returns A {@link TableDefinition} with this vector index included.\n   */\n  vectorIndex<\n    IndexName extends string,\n    VectorField extends ExtractFieldPaths<DocumentType>,\n    FilterFields extends ExtractFieldPaths<DocumentType> = never,\n  >(\n    name: IndexName,\n    indexConfig: Expand<VectorIndexConfig<VectorField, FilterFields>>,\n  ): TableDefinition<\n    DocumentType,\n    Indexes,\n    SearchIndexes,\n    Expand<\n      VectorIndexes &\n        Record<\n          IndexName,\n          {\n            vectorField: VectorField;\n            dimensions: number;\n            filterFields: FilterFields;\n          }\n        >\n    >\n  > {\n    this.vectorIndexes.push({\n      indexDescriptor: name,\n      vectorField: indexConfig.vectorField,\n      dimensions: indexConfig.dimensions,\n      filterFields: indexConfig.filterFields || [],\n    });\n    return this;\n  }\n\n  /**\n   * Work around for https://github.com/microsoft/TypeScript/issues/57035\n   */\n  protected self(): TableDefinition<\n    DocumentType,\n    Indexes,\n    SearchIndexes,\n    VectorIndexes\n  > {\n    return this;\n  }\n  /**\n   * Export the contents of this definition.\n   *\n   * This is called internally by the Convex framework.\n   * @internal\n   */\n  export() {\n    const documentType = this.validator.json;\n    if (typeof documentType !== \"object\") {\n      throw new Error(\n        \"Invalid validator: please make sure that the parameter of `defineTable` is valid (see https://docs.convex.dev/database/schemas)\",\n      );\n    }\n\n    return {\n      indexes: this.indexes,\n      searchIndexes: this.searchIndexes,\n      vectorIndexes: this.vectorIndexes,\n      documentType,\n    };\n  }\n}\n\n/**\n * Define a table in a schema.\n *\n * You can either specify the schema of your documents as an object like\n * ```ts\n * defineTable({\n *   field: v.string()\n * });\n * ```\n *\n * or as a schema type like\n * ```ts\n * defineTable(\n *  v.union(\n *    v.object({...}),\n *    v.object({...})\n *  )\n * );\n * ```\n *\n * @param documentSchema - The type of documents stored in this table.\n * @returns A {@link TableDefinition} for the table.\n *\n * @public\n */\nexport function defineTable<\n  DocumentSchema extends Validator<Record<string, any>, \"required\", any>,\n>(documentSchema: DocumentSchema): TableDefinition<DocumentSchema>;\n/**\n * Define a table in a schema.\n *\n * You can either specify the schema of your documents as an object like\n * ```ts\n * defineTable({\n *   field: v.string()\n * });\n * ```\n *\n * or as a schema type like\n * ```ts\n * defineTable(\n *  v.union(\n *    v.object({...}),\n *    v.object({...})\n *  )\n * );\n * ```\n *\n * @param documentSchema - The type of documents stored in this table.\n * @returns A {@link TableDefinition} for the table.\n *\n * @public\n */\nexport function defineTable<\n  DocumentSchema extends Record<string, GenericValidator>,\n>(\n  documentSchema: DocumentSchema,\n): TableDefinition<VObject<ObjectType<DocumentSchema>, DocumentSchema>>;\nexport function defineTable<\n  DocumentSchema extends\n    | Validator<Record<string, any>, \"required\", any>\n    | Record<string, GenericValidator>,\n>(documentSchema: DocumentSchema): TableDefinition<any, any, any> {\n  if (isValidator(documentSchema)) {\n    return new TableDefinition(documentSchema);\n  } else {\n    return new TableDefinition(v.object(documentSchema));\n  }\n}\n\n/**\n * A type describing the schema of a Convex project.\n *\n * This should be constructed using {@link defineSchema}, {@link defineTable},\n * and {@link v}.\n * @public\n */\nexport type GenericSchema = Record<string, TableDefinition>;\n\n/**\n *\n * The definition of a Convex project schema.\n *\n * This should be produced by using {@link defineSchema}.\n * @public\n */\nexport class SchemaDefinition<\n  Schema extends GenericSchema,\n  StrictTableTypes extends boolean,\n> {\n  public tables: Schema;\n  public strictTableNameTypes!: StrictTableTypes;\n  private readonly schemaValidation: boolean;\n\n  /**\n   * @internal\n   */\n  constructor(tables: Schema, options?: DefineSchemaOptions<StrictTableTypes>) {\n    this.tables = tables;\n    this.schemaValidation =\n      options?.schemaValidation === undefined ? true : options.schemaValidation;\n  }\n\n  /**\n   * Export the contents of this definition.\n   *\n   * This is called internally by the Convex framework.\n   * @internal\n   */\n  export(): string {\n    return JSON.stringify({\n      tables: Object.entries(this.tables).map(([tableName, definition]) => {\n        const { indexes, searchIndexes, vectorIndexes, documentType } =\n          definition.export();\n        return {\n          tableName,\n          indexes,\n          searchIndexes,\n          vectorIndexes,\n          documentType,\n        };\n      }),\n      schemaValidation: this.schemaValidation,\n    });\n  }\n}\n\n/**\n * Options for {@link defineSchema}.\n *\n * @public\n */\nexport interface DefineSchemaOptions<StrictTableNameTypes extends boolean> {\n  /**\n   * Whether Convex should validate at runtime that all documents match\n   * your schema.\n   *\n   * If `schemaValidation` is `true`, Convex will:\n   * 1. Check that all existing documents match your schema when your schema\n   * is pushed.\n   * 2. Check that all insertions and updates match your schema during mutations.\n   *\n   * If `schemaValidation` is `false`, Convex will not validate that new or\n   * existing documents match your schema. You'll still get schema-specific\n   * TypeScript types, but there will be no validation at runtime that your\n   * documents match those types.\n   *\n   * By default, `schemaValidation` is `true`.\n   */\n  schemaValidation?: boolean;\n\n  /**\n   * Whether the TypeScript types should allow accessing tables not in the schema.\n   *\n   * If `strictTableNameTypes` is `true`, using tables not listed in the schema\n   * will generate a TypeScript compilation error.\n   *\n   * If `strictTableNameTypes` is `false`, you'll be able to access tables not\n   * listed in the schema and their document type will be `any`.\n   *\n   * `strictTableNameTypes: false` is useful for rapid prototyping.\n   *\n   * Regardless of the value of `strictTableNameTypes`, your schema will only\n   * validate documents in the tables listed in the schema. You can still create\n   * and modify other tables on the dashboard or in JavaScript mutations.\n   *\n   * By default, `strictTableNameTypes` is `true`.\n   */\n  strictTableNameTypes?: StrictTableNameTypes;\n}\n\n/**\n * Define the schema of this Convex project.\n *\n * This should be exported from a `schema.ts` file in your `convex/` directory\n * like:\n *\n * ```ts\n * export default defineSchema({\n *   ...\n * });\n * ```\n *\n * @param schema - A map from table name to {@link TableDefinition} for all of\n * the tables in this project.\n * @param options - Optional configuration. See {@link DefineSchemaOptions} for\n * a full description.\n * @returns The schema.\n *\n * @public\n */\nexport function defineSchema<\n  Schema extends GenericSchema,\n  StrictTableNameTypes extends boolean = true,\n>(\n  schema: Schema,\n  options?: DefineSchemaOptions<StrictTableNameTypes>,\n): SchemaDefinition<Schema, StrictTableNameTypes> {\n  return new SchemaDefinition(schema, options);\n}\n\n/**\n * Internal type used in Convex code generation!\n *\n * Convert a {@link SchemaDefinition} into a {@link server.GenericDataModel}.\n *\n * @public\n */\nexport type DataModelFromSchemaDefinition<\n  SchemaDef extends SchemaDefinition<any, boolean>,\n> = MaybeMakeLooseDataModel<\n  {\n    [TableName in keyof SchemaDef[\"tables\"] &\n      string]: SchemaDef[\"tables\"][TableName] extends TableDefinition<\n      infer DocumentType,\n      infer Indexes,\n      infer SearchIndexes,\n      infer VectorIndexes\n    >\n      ? {\n          // We've already added all of the system fields except for `_id`.\n          // Add that here.\n          document: Expand<IdField<TableName> & ExtractDocument<DocumentType>>;\n          fieldPaths:\n            | keyof IdField<TableName>\n            | ExtractFieldPaths<DocumentType>;\n          indexes: Expand<Indexes & SystemIndexes>;\n          searchIndexes: SearchIndexes;\n          vectorIndexes: VectorIndexes;\n        }\n      : never;\n  },\n  SchemaDef[\"strictTableNameTypes\"]\n>;\n\ntype MaybeMakeLooseDataModel<\n  DataModel extends GenericDataModel,\n  StrictTableNameTypes extends boolean,\n> = StrictTableNameTypes extends true\n  ? DataModel\n  : Expand<DataModel & AnyDataModel>;\n\nconst _systemSchema = defineSchema({\n  _scheduled_functions: defineTable({\n    name: v.string(),\n    args: v.array(v.any()),\n    scheduledTime: v.float64(),\n    completedTime: v.optional(v.float64()),\n    state: v.union(\n      v.object({ kind: v.literal(\"pending\") }),\n      v.object({ kind: v.literal(\"inProgress\") }),\n      v.object({ kind: v.literal(\"success\") }),\n      v.object({ kind: v.literal(\"failed\"), error: v.string() }),\n      v.object({ kind: v.literal(\"canceled\") }),\n    ),\n  }),\n  _storage: defineTable({\n    sha256: v.string(),\n    size: v.float64(),\n    contentType: v.optional(v.string()),\n  }),\n});\n\nexport interface SystemDataModel\n  extends DataModelFromSchemaDefinition<typeof _systemSchema> {}\n\nexport type SystemTableNames = TableNamesInDataModel<SystemDataModel>;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAgBO,SAAS,eAAe,IAAY,KAA+B;AACxE,MAAI,OAAO,WAAW,eAAe,OAAO,YAAY,QAAW;AACjE,UAAM,IAAI;MACR;IAEF;EACF;AACA,QAAM,YAAY,OAAO,QAAQ,IAAI,KAAK,UAAU,GAAG,CAAC;AACxD,SAAO,KAAK,MAAM,SAAS;AAC7B;AAEA,eAAsB,oBACpB,IACA,KACc;AACd,MAAI,OAAO,WAAW,eAAe,OAAO,iBAAiB,QAAW;AACtE,UAAM,IAAI;MACR;IAEF;EACF;AACA,MAAI;AACJ,MAAI;AACF,gBAAY,MAAM,OAAO,aAAa,IAAI,KAAK,UAAU,GAAG,CAAC;EAC/D,SAAS,GAAQ;AAMf,QAAI,EAAE,SAAS,QAAW;AACxB,YAAM,WAAW,IAAI,YAAY,EAAE,OAAO;AAC1C,eAAS,OAAO,aAAa,EAAE,IAAI;AACnC,YAAM;IACR;AACA,UAAM,IAAI,MAAM,EAAE,OAAO;EAC3B;AACA,SAAO,KAAK,MAAM,SAAS;AAC7B;AAWO,SAAS,iBAAiB,IAAY,KAA+B;AAC1E,MAAI,OAAO,WAAW,eAAe,OAAO,cAAc,QAAW;AACnE,UAAM,IAAI;MACR;IAEF;EACF;AACA,SAAO,OAAO,UAAU,IAAI,GAAG;AACjC;;;AClEA,SAAS,YACP,WACA,mBACA,MACA;AACA,QAAM,UAAU,mBAAmB,iBAAiB;AACpD,SAAO;IACL,GAAG;IACH,MAAM,aAAa,UAAU,IAAI,CAAC;IAClC;IACA;EACF;AACF;AAEO,SAAS,iBAAiB,WAAmB;AAClD,SAAO;IACL,UAAU,OACR,OACA,SACiB;AACjB,YAAM,SAAS,MAAM;QACnB;QACA,YAAY,WAAW,OAAO,IAAI;MACpC;AACA,aAAO,aAAa,MAAM;IAC5B;IACA,aAAa,OACX,UACA,SACiB;AACjB,YAAM,SAAS,MAAM;QACnB;QACA,YAAY,WAAW,UAAU,IAAI;MACvC;AACA,aAAO,aAAa,MAAM;IAC5B;IACA,WAAW,OACT,QACA,SACiB;AACjB,YAAM,SAAS,MAAM;QACnB;QACA,YAAY,WAAW,QAAQ,IAAI;MACrC;AACA,aAAO,aAAa,MAAM;IAC5B;EACF;AACF;;;;;;ACmBO,IAAe,mBAAf,MAA6D;;;;EAUlE,cAAc;AARd,kBAAA,MAAQ,eAAA;AAGR,kBAAA,MAAQ,QAAA;EAQR;AACF;;;ACvFO,SAAS,YACd,KACA,KACA,QACA,SACA;AACA,MAAI,QAAQ,QAAW;AACrB,UAAM,IAAI;MACR,oBAAoB,GAAG,MAAM,OAAO,WAAW,MAAM;IACvD;EACF;AACF;AAeO,SAAS,gCACd,KACA,KACA,QACA,SACA;AACA,MAAI,CAAC,OAAO,UAAU,GAAG,KAAK,MAAM,GAAG;AACrC,UAAM,IAAI;MACR,OAAO,GAAG,MAAM,OAAO,WAAW,MAAM;IAC1C;EACF;AACF;;;;;;AClBO,SAAS,wBACd,WACgD;AAChD,SAAO,OACL,WACA,WACA,UACG;AACH,gBAAY,WAAW,GAAG,gBAAgB,WAAW;AACrD,gBAAY,WAAW,GAAG,gBAAgB,WAAW;AACrD,gBAAY,OAAO,GAAG,gBAAgB,OAAO;AAC7C,QACE,CAAC,MAAM,UACP,CAAC,MAAM,QAAQ,MAAM,MAAM,KAC3B,MAAM,OAAO,WAAW,GACxB;AACA,YAAM,MAAM,oDAAoD;IAClE;AAEA,WAAO,MAAM,IAAI;MACf;MACA,YAAY,MAAM;MAClB;IACF,EAAE,QAAQ;EACZ;AACF;AAEO,IAAM,kBAAN,MAAsB;EAM3B,YACE,WACA,WACA,OACA;AATF,IAAAA,eAAA,MAAQ,WAAA;AACR,IAAAA,eAAA,MAAQ,OAAA;AASN,SAAK,YAAY;AACjB,UAAM,UAAU,MAAM,SAClB,oBAAoB,MAAM,OAAO,iBAAiB,CAAC,IACnD;AAEJ,SAAK,QAAQ;MACX,MAAM;MACN,OAAO;QACL;QACA,OAAO,MAAM;QACb,QAAQ,MAAM;QACd,aAAa;MACf;IACF;EACF;EAEA,MAAM,UAA+B;AACnC,QAAI,KAAK,MAAM,SAAS,YAAY;AAClC,YAAM,IAAI,MAAM,sDAAsD;IACxE;AACA,UAAM,QAAQ,KAAK,MAAM;AACzB,SAAK,QAAQ,EAAE,MAAM,WAAW;AAEhC,UAAM,EAAE,QAAQ,IAAI,MAAM,oBAAoB,4BAA4B;MACxE,WAAW,KAAK;MAChB;MACA;IACF,CAAC;AACD,WAAO;EACT;AACF;AAaO,IAAM,iBAAN,cAA6B,iBAAsB;EAExD,YAAY,OAAkB;AAC5B,UAAM;AAFR,IAAAA,eAAA,MAAQ,OAAA;AAGN,SAAK,QAAQ;EACf;EAEA,YAAuB;AACrB,WAAO,KAAK;EACd;AACF;AAEO,SAAS,oBACd,MACW;AACX,MAAI,gBAAgB,gBAAgB;AAClC,WAAO,KAAK,UAAU;EACxB,OAAO;AAGL,WAAO,EAAE,UAAU,wBAAwB,IAAyB,EAAE;EACxE;AACF;AAEO,IAAM,oBAGT;;EAGF,GACE,WACA,OAC2B;AAC3B,QAAI,OAAO,cAAc,UAAU;AACjC,YAAM,IAAI,MAAM,oDAAoD;IACtE;AACA,WAAO,IAAI,eAAe;MACxB,KAAK;QACH,oBAAoB,IAAI,eAAe,EAAE,QAAQ,UAAU,CAAC,CAAC;QAC7D,oBAAoB,KAAK;MAC3B;IACF,CAAC;EACH;;EAIA,MAAM,OAAqE;AACzE,WAAO,IAAI,eAAe,EAAE,KAAK,MAAM,IAAI,mBAAmB,EAAE,CAAC;EACnE;AACF;;;ACnJO,SAAS,UAAU,WAAyB;AACjD,SAAO;IACL,iBAAiB,YAAY;AAC3B,aAAO,MAAM,oBAAoB,uBAAuB;QACtD;MACF,CAAC;IACH;EACF;AACF;;;;;;ACMO,IAAe,aAAf,MAAuD;;;;EAU5D,cAAc;AARd,IAAAC,eAAA,MAAQ,eAAA;AAGR,IAAAA,eAAA,MAAQ,QAAA;EAQR;AACF;;;;;;ACpBO,IAAMC,kBAAN,cAA6B,WAAgB;EAElD,YAAY,OAAkB;AAC5B,UAAM;AAFR,IAAAC,eAAA,MAAQ,OAAA;AAGN,SAAK,QAAQ;EACf;EAEA,YAAuB;AACrB,WAAO,KAAK;EACd;AACF;AAEO,SAASC,qBACd,MACW;AACX,MAAI,gBAAgBF,iBAAgB;AAClC,WAAO,KAAK,UAAU;EACxB,OAAO;AAGL,WAAO,EAAE,UAAU,wBAAwB,IAAyB,EAAE;EACxE;AACF;AAEO,IAAMG,qBAAqD;;EAGhE,GACE,GACA,GACqB;AACrB,WAAO,IAAIH,gBAAe;MACxB,KAAK,CAACE,qBAAoB,CAAC,GAAGA,qBAAoB,CAAC,CAAC;IACtD,CAAC;EACH;EAEA,IACE,GACA,GACqB;AACrB,WAAO,IAAIF,gBAAe;MACxB,MAAM,CAACE,qBAAoB,CAAC,GAAGA,qBAAoB,CAAC,CAAC;IACvD,CAAC;EACH;EAEA,GACE,GACA,GACqB;AACrB,WAAO,IAAIF,gBAAe;MACxB,KAAK,CAACE,qBAAoB,CAAC,GAAGA,qBAAoB,CAAC,CAAC;IACtD,CAAC;EACH;EAEA,IACE,GACA,GACqB;AACrB,WAAO,IAAIF,gBAAe;MACxB,MAAM,CAACE,qBAAoB,CAAC,GAAGA,qBAAoB,CAAC,CAAC;IACvD,CAAC;EACH;EAEA,GACE,GACA,GACqB;AACrB,WAAO,IAAIF,gBAAe;MACxB,KAAK,CAACE,qBAAoB,CAAC,GAAGA,qBAAoB,CAAC,CAAC;IACtD,CAAC;EACH;EAEA,IACE,GACA,GACqB;AACrB,WAAO,IAAIF,gBAAe;MACxB,MAAM,CAACE,qBAAoB,CAAC,GAAGA,qBAAoB,CAAC,CAAC;IACvD,CAAC;EACH;;EAIA,IACE,GACA,GACe;AACf,WAAO,IAAIF,gBAAe;MACxB,MAAM,CAACE,qBAAoB,CAAC,GAAGA,qBAAoB,CAAC,CAAC;IACvD,CAAC;EACH;EAEA,IACE,GACA,GACe;AACf,WAAO,IAAIF,gBAAe;MACxB,MAAM,CAACE,qBAAoB,CAAC,GAAGA,qBAAoB,CAAC,CAAC;IACvD,CAAC;EACH;EAEA,IACE,GACA,GACe;AACf,WAAO,IAAIF,gBAAe;MACxB,MAAM,CAACE,qBAAoB,CAAC,GAAGA,qBAAoB,CAAC,CAAC;IACvD,CAAC;EACH;EAEA,IACE,GACA,GACe;AACf,WAAO,IAAIF,gBAAe;MACxB,MAAM,CAACE,qBAAoB,CAAC,GAAGA,qBAAoB,CAAC,CAAC;IACvD,CAAC;EACH;EAEA,IACE,GACA,GACe;AACf,WAAO,IAAIF,gBAAe;MACxB,MAAM,CAACE,qBAAoB,CAAC,GAAGA,qBAAoB,CAAC,CAAC;IACvD,CAAC;EACH;EAEA,IAA4B,GAAwC;AAClE,WAAO,IAAIF,gBAAe,EAAE,MAAME,qBAAoB,CAAC,EAAE,CAAC;EAC5D;;EAIA,OAAO,OAA+D;AACpE,WAAO,IAAIF,gBAAe,EAAE,MAAM,MAAM,IAAIE,oBAAmB,EAAE,CAAC;EACpE;EAEA,MAAM,OAA+D;AACnE,WAAO,IAAIF,gBAAe,EAAE,KAAK,MAAM,IAAIE,oBAAmB,EAAE,CAAC;EACnE;EAEA,IAAI,GAAoD;AACtD,WAAO,IAAIF,gBAAe,EAAE,MAAME,qBAAoB,CAAC,EAAE,CAAC;EAC5D;;EAGA,MAAM,WAAoC;AACxC,WAAO,IAAIF,gBAAe,EAAE,QAAQ,UAAU,CAAC;EACjD;AACF;;;;;;ACaO,IAAe,aAAf,MAA0B;;;;EAO/B,cAAc;AALd,IAAAI,eAAA,MAAQ,eAAA;EAQR;AACF;;;;;;ACzKO,IAAM,wBAAN,MAAM,+BACH,WAKV;EAGU,YACN,kBACA;AACA,UAAM;AALR,IAAAC,eAAA,MAAQ,kBAAA;AACR,IAAAA,eAAA,MAAQ,YAAA;AAKN,SAAK,mBAAmB;AACxB,SAAK,aAAa;EACpB;EAEA,OAAO,MAA6B;AAClC,WAAO,IAAI,uBAAsB,CAAC,CAAC;EACrC;EAEQ,UAAU;AAChB,QAAI,KAAK,YAAY;AACnB,YAAM,IAAI;QACR;MACF;IACF;AACA,SAAK,aAAa;EACpB;EAEA,GAAG,WAAmB,OAAc;AAClC,SAAK,QAAQ;AACb,WAAO,IAAI;MACT,KAAK,iBAAiB,OAAO;QAC3B,MAAM;QACN,WAAW;QACX,OAAO,wBAAwB,KAAK;MACtC,CAAC;IACH;EACF;EAEA,GAAG,WAAmB,OAAc;AAClC,SAAK,QAAQ;AACb,WAAO,IAAI;MACT,KAAK,iBAAiB,OAAO;QAC3B,MAAM;QACN,WAAW;QACX,OAAO,aAAa,KAAK;MAC3B,CAAC;IACH;EACF;EACA,IAAI,WAAmB,OAAc;AACnC,SAAK,QAAQ;AACb,WAAO,IAAI;MACT,KAAK,iBAAiB,OAAO;QAC3B,MAAM;QACN,WAAW;QACX,OAAO,aAAa,KAAK;MAC3B,CAAC;IACH;EACF;EACA,GAAG,WAAmB,OAAc;AAClC,SAAK,QAAQ;AACb,WAAO,IAAI;MACT,KAAK,iBAAiB,OAAO;QAC3B,MAAM;QACN,WAAW;QACX,OAAO,aAAa,KAAK;MAC3B,CAAC;IACH;EACF;EACA,IAAI,WAAmB,OAAc;AACnC,SAAK,QAAQ;AACb,WAAO,IAAI;MACT,KAAK,iBAAiB,OAAO;QAC3B,MAAM;QACN,WAAW;QACX,OAAO,aAAa,KAAK;MAC3B,CAAC;IACH;EACF;EAEA,SAAS;AACP,SAAK,QAAQ;AACb,WAAO,KAAK;EACd;AACF;;;;;;ACzBO,IAAe,eAAf,MAA4B;;;;EAOjC,cAAc;AALd,IAAAC,eAAA,MAAQ,iBAAA;EAQR;AACF;;;;;;AC/DO,IAAM,0BAAN,MAAM,iCACH,aAIV;EAGU,YAAY,SAAgD;AAClE,UAAM;AAHR,IAAAC,eAAA,MAAQ,SAAA;AACR,IAAAA,eAAA,MAAQ,YAAA;AAGN,SAAK,UAAU;AACf,SAAK,aAAa;EACpB;EAEA,OAAO,MAA+B;AACpC,WAAO,IAAI,yBAAwB,CAAC,CAAC;EACvC;EAEQ,UAAU;AAChB,QAAI,KAAK,YAAY;AACnB,YAAM,IAAI;QACR;MACF;IACF;AACA,SAAK,aAAa;EACpB;EAEA,OACE,WACA,OACkE;AAClE,gBAAY,WAAW,GAAG,UAAU,WAAW;AAC/C,gBAAY,OAAO,GAAG,UAAU,OAAO;AACvC,SAAK,QAAQ;AACb,WAAO,IAAI;MACT,KAAK,QAAQ,OAAO;QAClB,MAAM;QACN,WAAW;QACX,OAAO;MACT,CAAC;IACH;EACF;EACA,GACE,WACA,OACkE;AAClE,gBAAY,WAAW,GAAG,MAAM,WAAW;AAE3C,QAAI,UAAU,WAAW,GAAG;AAC1B,kBAAY,OAAO,GAAG,UAAU,OAAO;IACzC;AACA,SAAK,QAAQ;AACb,WAAO,IAAI;MACT,KAAK,QAAQ,OAAO;QAClB,MAAM;QACN,WAAW;QACX,OAAO,wBAAwB,KAAK;MACtC,CAAC;IACH;EACF;EAEA,SAAS;AACP,SAAK,QAAQ;AACb,WAAO,KAAK;EACd;AACF;;;;;;ACrEA,IAAM,sBAAsB;AAsBrB,IAAM,uBAAN,MAEP;EAGE,YAAY,WAAmB;AAF/B,IAAAC,eAAA,MAAQ,WAAA;AAGN,SAAK,YAAY;EACnB;EAEA,UACE,WACA,YACW;AACX,gBAAY,WAAW,GAAG,aAAa,WAAW;AAClD,QAAI,eAAe,sBAAsB,IAAI;AAC7C,QAAI,eAAe,QAAW;AAC5B,qBAAe,WAAW,YAAY;IACxC;AACA,WAAO,IAAI,UAAU;MACnB,QAAQ;QACN,MAAM;QACN,WAAW,KAAK,YAAY,MAAM;QAClC,OAAO,aAAa,OAAO;QAC3B,OAAO;MACT;MACA,WAAW,CAAC;IACd,CAAC;EACH;EAEA,gBACE,WACA,cACW;AACX,gBAAY,WAAW,GAAG,mBAAmB,WAAW;AACxD,gBAAY,cAAc,GAAG,mBAAmB,cAAc;AAC9D,UAAM,sBAAsB,wBAAwB,IAAI;AACxD,WAAO,IAAI,UAAU;MACnB,QAAQ;QACN,MAAM;QACN,WAAW,KAAK,YAAY,MAAM;QAClC,SAAS,aAAa,mBAAmB,EAAE,OAAO;MACpD;MACA,WAAW,CAAC;IACd,CAAC;EACH;EAEA,gBAA2B;AACzB,WAAO,IAAI,UAAU;MACnB,QAAQ;QACN,MAAM;QACN,WAAW,KAAK;QAChB,OAAO;MACT;MACA,WAAW,CAAC;IACd,CAAC;EACH;EAEA,MAAM,OAAkC;AACtC,WAAO,KAAK,cAAc,EAAE,MAAM,KAAK;EACzC;;EAGA,MAAM,QAAyB;AAC7B,UAAM,cAAc,MAAM,oBAAoB,aAAa;MACzD,OAAO,KAAK;IACd,CAAC;AACD,UAAM,gBAAgB,aAAa,WAAW;AAC9C,WAAO;EACT;EAEA,OACE,WAGA;AACA,WAAO,KAAK,cAAc,EAAE,OAAO,SAAS;EAC9C;EAEA,MAAM,GAAW;AACf,WAAO,KAAK,cAAc,EAAE,MAAM,CAAC;EACrC;EAEA,UAA0B;AACxB,WAAO,KAAK,cAAc,EAAE,QAAQ;EACtC;EAEA,KAAK,GAAgC;AACnC,WAAO,KAAK,cAAc,EAAE,KAAK,CAAC;EACpC;EAEA,SAAS,gBAAmE;AAC1E,WAAO,KAAK,cAAc,EAAE,SAAS,cAAc;EACrD;EAEA,QAAsB;AACpB,WAAO,KAAK,cAAc,EAAE,MAAM;EACpC;EAEA,SAAuB;AACrB,WAAO,KAAK,cAAc,EAAE,OAAO;EACrC;EAEA,CAAC,OAAO,aAAa,IAAgC;AACnD,WAAO,KAAK,cAAc,EAAE,OAAO,aAAa,EAAE;EACpD;AACF;AAMA,SAAS,iBAAiB,MAAoC;AAC5D,QAAM,IAAI;IACR,SAAS,aACL,yDACA;EACN;AACF;AAEO,IAAM,YAAN,MAAM,WAA6C;EAOxD,YAAY,OAAwB;AANpC,IAAAA,eAAA,MAAQ,OAAA;AAON,SAAK,QAAQ,EAAE,MAAM,aAAa,MAAM;EAC1C;EAEQ,YAA6B;AACnC,QAAI,KAAK,MAAM,SAAS,aAAa;AACnC,YAAM,IAAI;QACR;MACF;IACF;AACA,UAAM,QAAQ,KAAK,MAAM;AACzB,SAAK,QAAQ,EAAE,MAAM,SAAS;AAC9B,WAAO;EACT;EAEQ,aAAqB;AAC3B,QAAI,KAAK,MAAM,SAAS,aAAa;AACnC,YAAM,IAAI,MAAM,2CAA2C;IAC7D;AACA,QAAI,KAAK,MAAM,SAAS,YAAY,KAAK,MAAM,SAAS,YAAY;AAClE,uBAAiB,KAAK,MAAM,IAAI;IAClC;AACA,UAAM,QAAQ,KAAK,MAAM;AACzB,UAAM,EAAE,QAAQ,IAAI,eAAe,mBAAmB,EAAE,OAAO,QAAQ,CAAC;AACxE,SAAK,QAAQ,EAAE,MAAM,aAAa,QAAQ;AAC1C,WAAO;EACT;EAEQ,aAAa;AACnB,QAAI,KAAK,MAAM,SAAS,aAAa;AACnC,YAAM,UAAU,KAAK,MAAM;AAC3B,qBAAe,oBAAoB,EAAE,QAAQ,CAAC;IAChD;AACA,SAAK,QAAQ,EAAE,MAAM,WAAW;EAClC;EAEA,MAAM,OAAkC;AACtC,gBAAY,OAAO,GAAG,SAAS,OAAO;AACtC,UAAM,QAAQ,KAAK,UAAU;AAC7B,QAAI,MAAM,OAAO,SAAS,UAAU;AAClC,YAAM,IAAI;QACR;MACF;IACF;AACA,QAAI,MAAM,OAAO,UAAU,MAAM;AAC/B,YAAM,IAAI,MAAM,6CAA6C;IAC/D;AACA,UAAM,OAAO,QAAQ;AACrB,WAAO,IAAI,WAAU,KAAK;EAC5B;EAEA,OACE,WAGK;AACL,gBAAY,WAAW,GAAG,UAAU,WAAW;AAC/C,UAAM,QAAQ,KAAK,UAAU;AAC7B,QAAI,MAAM,UAAU,UAAU,qBAAqB;AACjD,YAAM,IAAI;QACR,wCAAwC,mBAAmB;MAC7D;IACF;AACA,UAAM,UAAU,KAAK;MACnB,QAAQC,qBAAoB,UAAUC,kBAAiB,CAAC;IAC1D,CAAC;AACD,WAAO,IAAI,WAAU,KAAK;EAC5B;EAEA,MAAM,GAAgB;AACpB,gBAAY,GAAG,GAAG,SAAS,GAAG;AAC9B,UAAM,QAAQ,KAAK,UAAU;AAC7B,UAAM,UAAU,KAAK,EAAE,OAAO,EAAE,CAAC;AACjC,WAAO,IAAI,WAAU,KAAK;EAC5B;EAEA,CAAC,OAAO,aAAa,IAAgC;AACnD,SAAK,WAAW;AAChB,WAAO;EACT;EAEA,MAAM,OAAqC;AACzC,QAAI,KAAK,MAAM,SAAS,YAAY,KAAK,MAAM,SAAS,YAAY;AAClE,uBAAiB,KAAK,MAAM,IAAI;IAClC;AAIA,UAAM,UACJ,KAAK,MAAM,SAAS,cAAc,KAAK,WAAW,IAAI,KAAK,MAAM;AACnE,UAAM,EAAE,OAAO,KAAK,IAAI,MAAM,oBAAoB,uBAAuB;MACvE;IACF,CAAC;AACD,QAAI,MAAM;AACR,WAAK,WAAW;IAClB;AACA,UAAM,cAAc,aAAa,KAAK;AACtC,WAAO,EAAE,OAAO,aAAa,KAAK;EACpC;EAEA,SAAS;AACP,SAAK,WAAW;AAChB,WAAO,QAAQ,QAAQ,EAAE,MAAM,MAAM,OAAO,OAAU,CAAC;EACzD;EAEA,MAAM,SACJ,gBACgC;AAChC,gBAAY,gBAAgB,GAAG,YAAY,SAAS;AACpD,QACE,QAAO,iDAAgB,cAAa,YACpC,eAAe,WAAW,GAC1B;AACA,YAAM,IAAI;QACR,8DAA8D,iDAAgB,QAAQ;MACxF;IACF;AACA,UAAM,QAAQ,KAAK,UAAU;AAC7B,UAAM,WAAW,eAAe;AAChC,UAAM,SAAS,eAAe;AAC9B,UAAM,aAAY,iDAAgB,cAAa;AAC/C,UAAM,kBAAkB,eAAe,mBAAmB;AAC1D,UAAM,EAAE,MAAM,QAAQ,gBAAgB,aAAa,WAAW,IAC5D,MAAM,oBAAoB,iBAAiB;MACzC;MACA;MACA;MACA;MACA;MACA,kBAAkB,eAAe;MACjC;IACF,CAAC;AACH,WAAO;MACL,MAAM,KAAK,IAAI,CAAC,SAAiB,aAAa,IAAI,CAAC;MACnD;MACA;MACA;MACA;IACF;EACF;EAEA,MAAM,UAA+B;AACnC,UAAM,MAAe,CAAC;AACtB,qBAAiB,QAAQ,MAAM;AAC7B,UAAI,KAAK,IAAI;IACf;AACA,WAAO;EACT;EAEA,MAAM,KAAK,GAAgC;AACzC,gBAAY,GAAG,GAAG,QAAQ,GAAG;AAC7B,oCAAgC,GAAG,GAAG,QAAQ,GAAG;AACjD,WAAO,KAAK,MAAM,CAAC,EAAE,QAAQ;EAC/B;EAEA,MAAM,QAA6B;AACjC,UAAM,cAAc,MAAM,KAAK,KAAK,CAAC;AACrC,WAAO,YAAY,WAAW,IAAI,OAAO,YAAY,CAAC;EACxD;EAEA,MAAM,SAA8B;AAClC,UAAM,kBAAkB,MAAM,KAAK,KAAK,CAAC;AACzC,QAAI,gBAAgB,WAAW,GAAG;AAChC,aAAO;IACT;AACA,QAAI,gBAAgB,WAAW,GAAG;AAChC,YAAM,IAAI,MAAM;IAClB,gBAAgB,CAAC,EAAE,GAAG,KAAK,gBAAgB,CAAC,EAAE,GAAG,QAAQ;IACzD;AACA,WAAO,gBAAgB,CAAC;EAC1B;AACF;;;ACjUA,eAAe,IAAI,IAAuB,UAAmB;AAC3D,cAAY,IAAI,GAAG,OAAO,IAAI;AAC9B,MAAI,OAAO,OAAO,UAAU;AAC1B,UAAM,IAAI;MACR,oEAAoE,OAAO,EAAE,MAC3E,EACF;IACF;EACF;AACA,QAAM,OAAO;IACX,IAAI,aAAa,EAAE;IACnB;IACA;EACF;AACA,QAAM,cAAc,MAAM,oBAAoB,WAAW,IAAI;AAE7D,SAAO,aAAa,WAAW;AACjC;AAEO,SAAS,cAAuD;AACrE,QAAM,SAAS,CACb,WAAW,UAEyC;AACpD,WAAO;MACL,KAAK,OAAO,OAA0B;AACpC,eAAO,MAAM,IAAI,IAAI,QAAQ;MAC/B;MACA,OAAO,CAAC,cAAsB;AAC5B,eAAO,IAAI,YAAY,WAAW,QAAQ,EAAE,MAAM;MACpD;MACA,aAAa,CACX,WACA,OACgC;AAChC,oBAAY,WAAW,GAAG,eAAe,WAAW;AACpD,oBAAY,IAAI,GAAG,eAAe,IAAI;AACtC,cAAM,uBAAuB,UAAU,WAAW,GAAG;AACrD,YAAI,yBAAyB,UAAU;AACrC,gBAAM,IAAI;YACR,GACE,uBAAuB,WAAW,MACpC,wCACE,WAAW,KAAK,SAClB;UACF;QACF;AACA,cAAM,cAAc,eAAe,sBAAsB;UACvD,OAAO;UACP,UAAU;QACZ,CAAC;AACD,cAAM,gBAAgB,aAAa,WAAW;AAC9C,eAAO,cAAc;MACvB;;MAEA,QAAQ;MACR,OAAO,CAAC,cAAc;AACpB,eAAO,IAAI,YAAY,WAAW,QAAQ;MAC5C;IACF;EACF;AACA,QAAM,EAAE,QAAQ,GAAG,GAAG,KAAK,IAAI,OAAO,IAAI;AAC1C,QAAM,IAAI,OAAO;AACjB,IAAE,SAAS;AACX,SAAO;AACT;AAEA,eAAe,OAAO,WAAmB,OAAY;AACnD,MAAI,UAAU,WAAW,GAAG,GAAG;AAC7B,UAAM,IAAI,MAAM,kDAAkD;EACpE;AACA,cAAY,WAAW,GAAG,UAAU,OAAO;AAC3C,cAAY,OAAO,GAAG,UAAU,OAAO;AACvC,QAAM,cAAc,MAAM,oBAAoB,cAAc;IAC1D,OAAO;IACP,OAAO,aAAa,KAAK;EAC3B,CAAC;AACD,QAAM,gBAAgB,aAAa,WAAW;AAC9C,SAAO,cAAc;AACvB;AAEA,eAAe,MAAM,IAAS,OAAY;AACxC,cAAY,IAAI,GAAG,SAAS,IAAI;AAChC,cAAY,OAAO,GAAG,SAAS,OAAO;AACtC,QAAM,oBAAoB,oBAAoB;IAC5C,IAAI,aAAa,EAAE;IACnB,OAAO,iBAAiB,KAAc;EACxC,CAAC;AACH;AAEA,eAAe,QAAQ,IAAS,OAAY;AAC1C,cAAY,IAAI,GAAG,WAAW,IAAI;AAClC,cAAY,OAAO,GAAG,WAAW,OAAO;AACxC,QAAM,oBAAoB,eAAe;IACvC,IAAI,aAAa,EAAE;IACnB,OAAO,aAAa,KAAK;EAC3B,CAAC;AACH;AAEA,eAAe,QAAQ,IAAS;AAC9B,cAAY,IAAI,GAAG,UAAU,IAAI;AACjC,QAAM,oBAAoB,cAAc,EAAE,IAAI,aAAa,EAAE,EAAE,CAAC;AAClE;AAEO,SAAS,cACmC;AACjD,QAAM,SAAS,YAAY;AAC3B,SAAO;IACL,KAAK,OAAO;IACZ,OAAO,OAAO;IACd,aAAa,OAAO;IACpB,QAAQ,OAAO;IACf,QAAQ,OAAO,OAAO,UAAU;AAC9B,aAAO,MAAM,OAAO,OAAO,KAAK;IAClC;IACA,OAAO,OAAO,IAAI,UAAU;AAC1B,aAAO,MAAM,MAAM,IAAI,KAAK;IAC9B;IACA,SAAS,OAAO,IAAI,UAAU;AAC5B,aAAO,MAAM,QAAQ,IAAI,KAAK;IAChC;IACA,QAAQ,OAAO,OAAO;AACpB,aAAO,MAAM,QAAQ,EAAE;IACzB;IACA,OAAO,CAAC,cAAc;AACpB,aAAO,IAAI,YAAY,WAAW,KAAK;IACzC;EACF;AACF;AAEA,IAAM,cAAN,MAAkB;EAChB,YACqB,WACA,UACnB;AAFmB,SAAA,YAAA;AACA,SAAA,WAAA;EAClB;EAEH,MAAM,IAAI,IAAuB;AAC/B,WAAO,IAAI,IAAI,KAAK,QAAQ;EAC9B;EAEA,QAAQ;AACN,UAAM,uBAAuB,KAAK,UAAU,WAAW,GAAG;AAC1D,QAAI,yBAAyB,KAAK,UAAU;AAC1C,YAAM,IAAI;QACR,GACE,uBAAuB,WAAW,MACpC,wCACE,KAAK,WAAW,KAAK,SACvB;MACF;IACF;AACA,WAAO,IAAI,qBAAqB,KAAK,SAAS;EAChD;AACF;AAEA,IAAM,cAAN,cAA0B,YAAY;EACpC,MAAM,OAAO,OAAY;AACvB,WAAO,OAAO,KAAK,WAAW,KAAK;EACrC;EACA,MAAM,MAAM,IAAS,OAAY;AAC/B,WAAO,MAAM,IAAI,KAAK;EACxB;EACA,MAAM,QAAQ,IAAS,OAAY;AACjC,WAAO,QAAQ,IAAI,KAAK;EAC1B;EACA,MAAM,OAAO,IAAS;AACpB,WAAO,QAAQ,EAAE;EACnB;AACF;;;AClLO,SAAS,yBAAoC;AAClD,SAAO;IACL,UAAU,OACR,SACA,mBACA,SACG;AACH,YAAMC,eAAc,oBAAoB,SAAS,mBAAmB,IAAI;AACxE,aAAO,MAAM,oBAAoB,gBAAgBA,YAAW;IAC9D;IACA,OAAO,OACL,wBACA,mBACA,SACG;AACH,YAAMA,eAAc;QAClB;QACA;QACA;MACF;AACA,aAAO,MAAM,oBAAoB,gBAAgBA,YAAW;IAC9D;IACA,QAAQ,OAAO,OAAmC;AAChD,kBAAY,IAAI,GAAG,UAAU,IAAI;AACjC,YAAM,OAAO,EAAE,IAAI,aAAa,EAAE,EAAE;AACpC,YAAM,oBAAoB,kBAAkB,IAAI;IAClD;EACF;AACF;AAEO,SAAS,qBAAqB,WAA8B;AACjE,SAAO;IACL,UAAU,OACR,SACA,mBACA,SACG;AACH,YAAMA,eAAc;QAClB;QACA,GAAG,oBAAoB,SAAS,mBAAmB,IAAI;MACzD;AACA,aAAO,MAAM,oBAAoB,wBAAwBA,YAAW;IACtE;IACA,OAAO,OACL,wBACA,mBACA,SACG;AACH,YAAMA,eAAc;QAClB;QACA,GAAG,iBAAiB,wBAAwB,mBAAmB,IAAI;MACrE;AACA,aAAO,MAAM,oBAAoB,wBAAwBA,YAAW;IACtE;IACA,QAAQ,OAAO,OAAmC;AAChD,kBAAY,IAAI,GAAG,UAAU,IAAI;AACjC,YAAMA,eAAc,EAAE,IAAI,aAAa,EAAE,EAAE;AAC3C,aAAO,MAAM,oBAAoB,0BAA0BA,YAAW;IACxE;EACF;AACF;AAEA,SAAS,oBACP,SACA,mBACA,MACA;AACA,MAAI,OAAO,YAAY,UAAU;AAC/B,UAAM,IAAI,MAAM,4BAA4B;EAC9C;AACA,MAAI,CAAC,SAAS,OAAO,GAAG;AACtB,UAAM,IAAI,MAAM,mCAAmC;EACrD;AACA,MAAI,UAAU,GAAG;AACf,UAAM,IAAI,MAAM,gCAAgC;EAClD;AACA,QAAM,eAAe,UAAU,IAAI;AACnC,QAAM,UAAU,mBAAmB,iBAAiB;AAEpD,QAAM,MAAM,KAAK,IAAI,IAAI,WAAW;AACpC,SAAO;IACL,GAAG;IACH;IACA,MAAM,aAAa,YAAY;IAC/B;EACF;AACF;AAEA,SAAS,iBACP,wBACA,mBACA,MACA;AACA,MAAI;AACJ,MAAI,kCAAkC,MAAM;AAC1C,SAAK,uBAAuB,QAAQ,IAAI;EAC1C,WAAW,OAAO,2BAA2B,UAAU;AAGrD,SAAK,yBAAyB;EAChC,OAAO;AACL,UAAM,IAAI,MAAM,4CAA4C;EAC9D;AACA,QAAM,UAAU,mBAAmB,iBAAiB;AACpD,QAAM,eAAe,UAAU,IAAI;AACnC,SAAO;IACL,GAAG;IACH;IACA,MAAM,aAAa,YAAY;IAC/B;EACF;AACF;;;AC7GO,SAAS,mBAAmB,WAAkC;AACnE,SAAO;IACL,QAAQ,OAAO,cAA6B;AAC1C,kBAAY,WAAW,GAAG,UAAU,WAAW;AAC/C,aAAO,MAAM,oBAAoB,qBAAqB;QACpD;QACA;QACA;MACF,CAAC;IACH;IACA,aAAa,OAAO,cAAoD;AACtE,aAAO,MAAM,oBAAoB,0BAA0B;QACzD;QACA;QACA;MACF,CAAC;IACH;EACF;AACF;AAEO,SAAS,mBAAmB,WAAkC;AACnE,QAAM,SAAS,mBAAmB,SAAS;AAC3C,SAAO;IACL,mBAAmB,YAAY;AAC7B,aAAO,MAAM,oBAAoB,gCAAgC;QAC/D;QACA;MACF,CAAC;IACH;IACA,QAAQ,OAAO,cAA6B;AAC1C,YAAM,oBAAoB,qBAAqB;QAC7C;QACA;QACA;MACF,CAAC;IACH;IACA,QAAQ,OAAO;IACf,aAAa,OAAO;EACtB;AACF;AAEO,SAAS,yBACd,WACqB;AACrB,QAAM,SAAS,mBAAmB,SAAS;AAC3C,SAAO;IACL,GAAG;IACH,OAAO,OAAO,MAAY,YAAkC;AAC1D,aAAO,MAAM,iBAAiB,qBAAqB;QACjD;QACA;QACA;QACA;MACF,CAAC;IACH;IACA,KAAK,OAAO,cAA6B;AACvC,aAAO,MAAM,iBAAiB,mBAAmB;QAC/C;QACA;QACA;MACF,CAAC;IACH;EACF;AACF;;;AChCA,eAAe,eAEb,MAAS,SAAiB;AAG1B,QAAM,YAAY;AAClB,QAAM,OAAO,aAAa,KAAK,MAAM,OAAO,CAAC;AAC7C,QAAM,cAAc;IAClB,IAAI,YAAY;IAChB,MAAM,UAAU,SAAS;IACzB,SAAS,mBAAmB,SAAS;IACrC,WAAW,uBAAuB;IAElC,UAAU,CAAC,WAAgBC,UAAe,OAAO,SAAS,WAAWA,KAAI;IACzE,aAAa,CAAC,WAAgBA,UAC5B,OAAO,YAAY,WAAWA,KAAI;EACtC;AACA,QAAM,SAAS,MAAM,eAAe,MAAM,aAAa,IAAW;AAClE,sBAAoB,MAAM;AAC1B,SAAO,KAAK,UAAU,aAAa,WAAW,SAAY,OAAO,MAAM,CAAC;AAC1E;AAEO,SAAS,oBAAoBC,IAAQ;AAC1C,MAAIA,cAAa,wBAAwBA,cAAa,WAAW;AAC/D,UAAM,IAAI;MACR;IACF;EACF;AACF;AAEA,eAAsB,eAIpB,MAAS,KAAU,MAAY;AAC/B,MAAI;AACJ,MAAI;AACF,aAAS,MAAM,QAAQ,QAAQ,KAAK,KAAK,GAAG,IAAI,CAAC;EACnD,SAAS,QAAiB;AACxB,UAAM,yBAAyB,MAAM;EACvC;AACA,SAAO;AACT;AAEA,SAAS,iBACP,UACA,SACS;AACT,SAAO,CAAC,KAAU,SAAc;AAC9B,eAAW,QAAQ;MACjB,2IAC+B,QAAQ;IAEzC;AACA,WAAO,QAAQ,KAAK,IAAI;EAC1B;AACF;AAGA,SAAS,yBAAyB,QAAiB;AACjD,MACE,OAAO,WAAW,YAClB,WAAW,QACX,OAAO,IAAI,aAAa,KAAK,QAC7B;AACA,UAAM,QAAQ;AACd,UAAM,OAAO,KAAK;MAChB,aAAa,MAAM,SAAS,SAAY,OAAO,MAAM,IAAI;IAC3D;AACC,UAAc,oBAAoB,OAAO,IAAI,aAAa;AAC3D,WAAO;EACT,OAAO;AACL,WAAO;EACT;AACF;AAOA,SAAS,mBAAmB;;AAC1B,MACE,OAAO,WAAW,eAClB,CAAE,OAAe,iCACjB;AACA;EACF;AAEA,QAAM,kBACJ,kBAAO,yBAAyB,YAAY,QAAQ,MAApD,mBACI,QADJ,mBACS,WACN,SAAS,qBAAoB;AAClC,MAAI,eAAe;AACjB,UAAM,IAAI,MAAM,yDAAyD;EAC3E;AACF;AAUA,SAAS,WAAW,oBAAwC;AAC1D,SAAO,MAAM;AACX,QAAI,OAAyB,EAAE,IAAI;AACnC,QACE,OAAO,uBAAuB,YAC9B,mBAAmB,SAAS,QAC5B;AACA,aAAO,kBAAkB,mBAAmB,IAAI;IAClD;AACA,WAAO,KAAK,UAAU,KAAK,IAAI;EACjC;AACF;AAEA,SAAS,cAAc,oBAAwC;AAC7D,SAAO,MAAM;AACX,QAAI;AACJ,QACE,OAAO,uBAAuB,YAC9B,mBAAmB,YAAY,QAC/B;AACA,gBAAU,kBAAkB,mBAAmB,OAAO;IACxD;AACA,WAAO,KAAK,UAAU,UAAU,QAAQ,OAAO,IAAI;EACrD;AACF;AAeO,IAAM,kBAAmD,CAC9D,uBACG;AACH,QAAM,UACJ,OAAO,uBAAuB,aAC1B,qBACA,mBAAmB;AAEzB,QAAM,OAAO,iBAAiB,YAAY,OAAO;AAMjD,mBAAiB;AACjB,OAAK,aAAa;AAClB,OAAK,WAAW;AAChB,OAAK,iBAAiB,CAAC,YAAY,eAAe,SAAS,OAAO;AAClE,OAAK,aAAa,WAAW,kBAAkB;AAC/C,OAAK,gBAAgB,cAAc,kBAAkB;AACrD,OAAK,WAAW;AAChB,SAAO;AACT;AAeO,IAAM,0BAA6D,CACxE,uBACG;AACH,QAAM,UACJ,OAAO,uBAAuB,aAC1B,qBACA,mBAAmB;AAEzB,QAAM,OAAO;IACX;IACA;EACF;AAEA,mBAAiB;AACjB,OAAK,aAAa;AAClB,OAAK,aAAa;AAClB,OAAK,iBAAiB,CAAC,YAAY,eAAe,SAAS,OAAO;AAClE,OAAK,aAAa,WAAW,kBAAkB;AAC/C,OAAK,gBAAgB,cAAc,kBAAkB;AACrD,OAAK,WAAW;AAChB,SAAO;AACT;AAEA,eAAe,YAEb,MAAS,SAAiB;AAG1B,QAAM,YAAY;AAClB,QAAM,OAAO,aAAa,KAAK,MAAM,OAAO,CAAC;AAC7C,QAAM,WAAW;IACf,IAAI,YAAY;IAChB,MAAM,UAAU,SAAS;IACzB,SAAS,mBAAmB,SAAS;IACrC,UAAU,CAAC,WAAgBD,UAAe,OAAO,SAAS,WAAWA,KAAI;EAC3E;AACA,QAAM,SAAS,MAAM,eAAe,MAAM,UAAU,IAAW;AAC/D,sBAAoB,MAAM;AAC1B,SAAO,KAAK,UAAU,aAAa,WAAW,SAAY,OAAO,MAAM,CAAC;AAC1E;AAeO,IAAM,eAA6C,CACxD,uBACG;AACH,QAAM,UACJ,OAAO,uBAAuB,aAC1B,qBACA,mBAAmB;AAEzB,QAAM,OAAO,iBAAiB,SAAS,OAAO;AAM9C,mBAAiB;AACjB,OAAK,UAAU;AACf,OAAK,WAAW;AAChB,OAAK,cAAc,CAAC,YAAY,YAAY,SAAS,OAAO;AAC5D,OAAK,aAAa,WAAW,kBAAkB;AAC/C,OAAK,gBAAgB,cAAc,kBAAkB;AACrD,OAAK,WAAW;AAChB,SAAO;AACT;AAeO,IAAM,uBAAuD,CAClE,uBACG;AACH,QAAM,UACJ,OAAO,uBAAuB,aAC1B,qBACA,mBAAmB;AAEzB,QAAM,OAAO,iBAAiB,iBAAiB,OAAO;AAMtD,mBAAiB;AACjB,OAAK,UAAU;AACf,OAAK,aAAa;AAClB,OAAK,cAAc,CAAC,YAAY,YAAY,SAAgB,OAAO;AACnE,OAAK,aAAa,WAAW,kBAAkB;AAC/C,OAAK,gBAAgB,cAAc,kBAAkB;AACrD,OAAK,WAAW;AAChB,SAAO;AACT;AAEA,eAAe,aAEb,MAAS,WAAmB,SAAiB;AAC7C,QAAM,OAAO,aAAa,KAAK,MAAM,OAAO,CAAC;AAC7C,QAAM,QAAQ,iBAAiB,SAAS;AACxC,QAAM,MAAM;IACV,GAAG;IACH,MAAM,UAAU,SAAS;IACzB,WAAW,qBAAqB,SAAS;IACzC,SAAS,yBAAyB,SAAS;IAC3C,cAAc,wBAAwB,SAAS;EACjD;AACA,QAAM,SAAS,MAAM,eAAe,MAAM,KAAK,IAAW;AAC1D,SAAO,KAAK,UAAU,aAAa,WAAW,SAAY,OAAO,MAAM,CAAC;AAC1E;AAaO,IAAM,gBAA+C,CAC1D,uBACG;AACH,QAAM,UACJ,OAAO,uBAAuB,aAC1B,qBACA,mBAAmB;AAEzB,QAAM,OAAO,iBAAiB,UAAU,OAAO;AAM/C,mBAAiB;AACjB,OAAK,WAAW;AAChB,OAAK,WAAW;AAChB,OAAK,eAAe,CAAC,WAAW,YAC9B,aAAa,SAAS,WAAW,OAAO;AAC1C,OAAK,aAAa,WAAW,kBAAkB;AAC/C,OAAK,gBAAgB,cAAc,kBAAkB;AACrD,OAAK,WAAW;AAChB,SAAO;AACT;AAaO,IAAM,wBAAyD,CACpE,uBACG;AACH,QAAM,UACJ,OAAO,uBAAuB,aAC1B,qBACA,mBAAmB;AAEzB,QAAM,OAAO,iBAAiB,kBAAkB,OAAO;AAMvD,mBAAiB;AACjB,OAAK,WAAW;AAChB,OAAK,aAAa;AAClB,OAAK,eAAe,CAAC,WAAW,YAC9B,aAAa,SAAS,WAAW,OAAO;AAC1C,OAAK,aAAa,WAAW,kBAAkB;AAC/C,OAAK,gBAAgB,cAAc,kBAAkB;AACrD,OAAK,WAAW;AAChB,SAAO;AACT;AAEA,eAAe,iBAEb,MAAS,SAAkB;AAG3B,QAAM,YAAY;AAClB,QAAM,QAAQ,iBAAiB,SAAS;AACxC,QAAM,MAAM;IACV,GAAG;IACH,MAAM,UAAU,SAAS;IACzB,SAAS,yBAAyB,SAAS;IAC3C,WAAW,qBAAqB,SAAS;IACzC,cAAc,wBAAwB,SAAS;EACjD;AACA,SAAO,MAAM,eAAe,MAAM,KAAK,CAAC,OAAO,CAAC;AAClD;AAWO,IAAM,oBAAoB,CAC/B,SAIqB;AACrB,QAAM,IAAI,iBAAiB,cAAc,IAAI;AAC7C,mBAAiB;AACjB,IAAE,SAAS;AACX,IAAE,mBAAmB,CAAC,YAAY,iBAAiB,MAAa,OAAO;AACvE,IAAE,WAAW;AACb,SAAO;AACT;AAEA,eAAe,OACb,SACA,GACA,MACc;AACd,QAAM,YAAY,UAAU,IAAI;AAChC,QAAME,eAAc;IAClB;IACA,MAAM,aAAa,SAAS;IAC5B,GAAG,mBAAmB,CAAC;EACzB;AACA,QAAM,SAAS,MAAM,oBAAoB,cAAcA,YAAW;AAClE,SAAO,aAAa,MAAM;AAC5B;;;AC9VO,IAAM,0BAA0B,EAAE,OAAO;EAC9C,UAAU,EAAE,OAAO;EACnB,QAAQ,EAAE,MAAM,EAAE,OAAO,GAAG,EAAE,KAAK,CAAC;EACpC,WAAW,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,GAAG,EAAE,KAAK,CAAC,CAAC;EACnD,IAAI,EAAE,SAAS,EAAE,OAAO,CAAC;EACzB,iBAAiB,EAAE,SAAS,EAAE,OAAO,CAAC;EACtC,kBAAkB,EAAE,SAAS,EAAE,OAAO,CAAC;AACzC,CAAC;;;;;;AChHD,IAAM,eAAe;EACnB;EACA;EACA;EACA;EACA;EACA;EACA;AACF;AAkJO,IAAM,WAAW,MAAM,IAAI,MAAM;AASxC,SAAS,uBAAuB,GAAW;AACzC,MAAI,CAAC,OAAO,UAAU,CAAC,KAAK,KAAK,GAAG;AAClC,UAAM,IAAI,MAAM,4CAA4C;EAC9D;AACF;AAEA,SAAS,oBAAoB,GAAW;AACtC,MAAI,CAAC,OAAO,UAAU,CAAC,KAAK,IAAI,KAAK,IAAI,IAAI;AAC3C,UAAM,IAAI,MAAM,8CAA8C;EAChE;AACA,SAAO;AACT;AAEA,SAAS,mBAAmB,GAAW;AACrC,MAAI,CAAC,aAAa,SAAS,CAAc,GAAG;AAC1C,UAAM,IAAI,MAAM,6CAA6C;EAC/D;AACA,SAAO;AACT;AAEA,SAAS,mBAAmB,GAAW;AACrC,MAAI,CAAC,OAAO,UAAU,CAAC,KAAK,IAAI,KAAK,IAAI,IAAI;AAC3C,UAAM,IAAI,MAAM,6CAA6C;EAC/D;AACA,SAAO;AACT;AAEA,SAAS,sBAAsB,GAAW;AACxC,MAAI,CAAC,OAAO,UAAU,CAAC,KAAK,IAAI,KAAK,IAAI,IAAI;AAC3C,UAAM,IAAI,MAAM,gDAAgD;EAClE;AACA,SAAO;AACT;AAEA,SAAS,oBAAoB,GAAW;AACtC,SAAO;AACT;AAEA,SAAS,wBAAwB,GAAW;AAC1C,MAAI,CAAC,EAAE,MAAM,UAAU,GAAG;AACxB,UAAM,IAAI;MACR,2BAA2B,CAAC;IAC9B;EACF;AACA,SAAO;AACT;AASO,IAAM,QAAN,MAAY;EAGjB,cAAc;AAFd,IAAAC,gBAAA,MAAA,OAAA;AACA,IAAAA,gBAAA,MAAA,SAAA;AAEE,SAAK,UAAU;AACf,SAAK,QAAQ,CAAC;EAChB;;EAGA,SACE,gBACA,UACA,mBACA,MACA;AACA,UAAM,WAAW,UAAU,IAAI;AAC/B,4BAAwB,cAAc;AACtC,QAAI,kBAAkB,KAAK,OAAO;AAChC,YAAM,IAAI,MAAM,qCAAqC,cAAc,EAAE;IACvE;AACA,SAAK,MAAM,cAAc,IAAI;MAC3B,MAAM,gBAAgB,iBAAiB;MACvC,MAAM,CAAC,aAAa,QAAQ,CAAC;MAC7B;IACF;EACF;;;;;;;;;;;;;;EAeA,SACE,gBACA,UACA,sBACG,MACH;AACA,UAAM,IAAI;AACV,UAAM,aAAa,EAAE,aAAa,KAAK,EAAE,YAAY;AACrD,UAAM,aAAa,EAAE,aAAa,KAAK,EAAE,YAAY;AACrD,UAAM,WAAW,EAAE,WAAW,KAAK,EAAE,UAAU;AAC/C,UAAM,QAAQ,aAAa,aAAa;AACxC,QAAI,UAAU,GAAG;AACf,YAAM,IAAI,MAAM,gDAAgD;IAClE;AACA,QAAI,YAAY;AACd,6BAAuB,SAAS,OAAQ;IAC1C,WAAW,YAAY;AACrB,6BAAuB,SAAS,OAAQ;IAC1C,WAAW,UAAU;AACnB,6BAAuB,SAAS,KAAM;IACxC;AACA,SAAK;MACH;MACA,EAAE,GAAG,UAAU,MAAM,WAAW;MAChC;MACA,GAAG;IACL;EACF;;;;;;;;;;;;;;;;;;;;EAqBA,OACE,gBACA,UACA,sBACG,MACH;AACA,UAAM,YAAY,sBAAsB,SAAS,SAAS;AAC1D,SAAK;MACH;MACA,EAAE,WAAW,MAAM,SAAS;MAC5B;MACA,GAAG;IACL;EACF;;;;;;;;;;;;;;;;;;;;;EAsBA,MACE,gBACA,UACA,sBACG,MACH;AACA,UAAM,UAAU,mBAAmB,SAAS,OAAO;AACnD,UAAM,YAAY,sBAAsB,SAAS,SAAS;AAC1D,SAAK;MACH;MACA,EAAE,SAAS,WAAW,MAAM,QAAQ;MACpC;MACA,GAAG;IACL;EACF;;;;;;;;;;;;;;;;;;;;;EAsBA,OACE,gBACA,UACA,sBACG,MACH;AACA,UAAM,YAAY,mBAAmB,SAAS,SAAS;AACvD,UAAM,UAAU,mBAAmB,SAAS,OAAO;AACnD,UAAM,YAAY,sBAAsB,SAAS,SAAS;AAC1D,SAAK;MACH;MACA,EAAE,WAAW,SAAS,WAAW,MAAM,SAAS;MAChD;MACA,GAAG;IACL;EACF;;;;;;;;;;;;;;;;;;;;;;;;;EA0BA,QACE,gBACA,UACA,sBACG,MACH;AACA,UAAM,MAAM,oBAAoB,SAAS,GAAG;AAC5C,UAAM,UAAU,mBAAmB,SAAS,OAAO;AACnD,UAAM,YAAY,sBAAsB,SAAS,SAAS;AAC1D,SAAK;MACH;MACA,EAAE,KAAK,SAAS,WAAW,MAAM,UAAU;MAC3C;MACA,GAAG;IACL;EACF;;;;;;;;;;;;;;;;;;;;;EAsBA,KACE,gBACA,MACA,sBACG,MACH;AACA,UAAM,IAAI,oBAAoB,IAAI;AAClC,SAAK;MACH;MACA,EAAE,MAAM,GAAG,MAAM,OAAO;MACxB;MACA,GAAG;IACL;EACF;;EAGA,SAAS;AACP,WAAO,KAAK,UAAU,KAAK,KAAK;EAClC;AACF;;;;;;ACreO,IAAM,wBAAwB;EACnC;EACA;EACA;EACA;EACA;EACA;AACF;AAYO,SAAS,gBACd,QACgB;AAGhB,MAAI,WAAW,OAAQ,QAAO;AAC9B,SAAO;AACT;AAOO,IAAM,aAAa,MAAM,IAAI,WAAW;AA+FxC,IAAM,aAAN,MAAiB;EAAjB,cAAA;AACL,IAAAC,gBAAA,MAAA,eAAkE,oBAAI,IAAI,CAAA;AAC1E,IAAAA,gBAAA,MAAA,gBAAmE,oBAAI,IAAI,CAAA;AAC3E,IAAAA,gBAAA,MAAA,YAAiB,IAAA;AAgBjB,IAAAA,gBAAA,MAAA,SAAQ,CAAC,SAAoB;AAC3B,UAAI,CAAC,KAAK,QAAS,OAAM,IAAI,MAAM,wBAAwB;AAC3D,UAAI,CAAC,KAAK,OAAQ,OAAM,IAAI,MAAM,uBAAuB;AACzD,YAAM,EAAE,QAAQ,QAAQ,IAAI;AAC5B,UAAI,CAAC,sBAAsB,SAAS,MAAM,GAAG;AAC3C,cAAM,IAAI;UACR,IAAI,MAAM;QACZ;MACF;AAEA,UAAI,UAAU,MAAM;AAClB,YAAI,gBAAgB,MAAM;AACxB,gBAAM,IAAI;YACR;UACF;QACF;AACA,YAAI,CAAC,KAAK,KAAK,WAAW,GAAG,GAAG;AAC9B,gBAAM,IAAI,MAAM,SAAS,KAAK,IAAI,2BAA2B;QAC/D;AACA,cAAM,UACJ,KAAK,YAAY,IAAI,KAAK,IAAI,IAC1B,KAAK,YAAY,IAAI,KAAK,IAAI,IAC9B,oBAAI,IAAI;AACd,YAAI,QAAQ,IAAI,MAAM,GAAG;AACvB,gBAAM,IAAI;YACR,SAAS,KAAK,IAAI,gBAAgB,MAAM;UAC1C;QACF;AACA,gBAAQ,IAAI,QAAQ,OAAO;AAC3B,aAAK,YAAY,IAAI,KAAK,MAAM,OAAO;MACzC,WAAW,gBAAgB,MAAM;AAC/B,YAAI,CAAC,KAAK,WAAW,WAAW,GAAG,GAAG;AACpC,gBAAM,IAAI;YACR,eAAe,KAAK,UAAU;UAChC;QACF;AACA,YAAI,CAAC,KAAK,WAAW,SAAS,GAAG,GAAG;AAClC,gBAAM,IAAI,MAAM,cAAc,KAAK,UAAU,oBAAoB;QACnE;AACA,cAAM,WACJ,KAAK,aAAa,IAAI,MAAM,KAAK,oBAAI,IAA8B;AACrE,YAAI,SAAS,IAAI,KAAK,UAAU,GAAG;AACjC,gBAAM,IAAI;YACR,GAAG,KAAK,MAAM,eAAe,KAAK,UAAU;UAC9C;QACF;AACA,iBAAS,IAAI,KAAK,YAAY,OAAO;AACrC,aAAK,aAAa,IAAI,QAAQ,QAAQ;MACxC,OAAO;AACL,cAAM,IAAI;UACR;QACF;MACF;IACF,CAAA;AASA,IAAAA,gBAAA,MAAA,aAAY,MAEP;AACH,YAAM,aAAuB,CAAC,GAAG,KAAK,YAAY,KAAK,CAAC,EAAE,KAAK;AAC/D,YAAM,QAAQ,WAAW;QAAQ,CAAC,SAChC,CAAC,GAAG,KAAK,YAAY,IAAI,IAAI,EAAG,KAAK,CAAC,EACnC,KAAK,EACL;UACC,CAAC,WACC,CAAC,MAAM,QAAQ,KAAK,YAAY,IAAI,IAAI,EAAG,IAAI,MAAM,CAAE;QAC3D;MACJ;AAEA,YAAM,oBAAoB,CAAC,GAAG,KAAK,aAAa,KAAK,CAAC,EAAE,KAAK;AAC7D,YAAM,WAAW,kBAAkB;QAAQ,CAAC,WAC1C,CAAC,GAAG,KAAK,aAAa,IAAI,MAAM,EAAG,KAAK,CAAC,EACtC,KAAK,EACL;UACC,CAAC,eACC;YACE,GAAG,UAAU;YACb;YACA,KAAK,aAAa,IAAI,MAAM,EAAG,IAAI,UAAU;UAC/C;QACJ;MACJ;AAEA,aAAO,CAAC,GAAG,OAAO,GAAG,QAAQ;IAC/B,CAAA;AAkBA,IAAAA,gBAAA,MAAA,UAAS,CACP,MACA,WACgE;;AAChE,eAAS,gBAAgB,MAAM;AAC/B,YAAM,cAAa,UAAK,YAAY,IAAI,IAAI,MAAzB,mBAA4B,IAAI;AACnD,UAAI,WAAY,QAAO,CAAC,YAAY,QAAQ,IAAI;AAEhD,YAAM,WAAW,KAAK,aAAa,IAAI,MAAM,KAAK,oBAAI,IAAI;AAC1D,YAAM,iBAAiB,CAAC,GAAG,SAAS,QAAQ,CAAC,EAAE;QAC7C,CAAC,CAAC,SAASC,GAAE,GAAG,CAAC,SAAS,EAAE,MAAM,QAAQ,SAAS,QAAQ;MAC7D;AACA,iBAAW,CAAC,YAAY,QAAQ,KAAK,gBAAgB;AACnD,YAAI,KAAK,WAAW,UAAU,GAAG;AAC/B,iBAAO,CAAC,UAAU,QAAQ,GAAG,UAAU,GAAG;QAC5C;MACF;AACA,aAAO;IACT,CAAA;AAWA,IAAAD,gBAAA,MAAA,cAAa,OACX,SACA,iBACoB;AACpB,YAAM,UAAU,iBAAiB,yBAAyB;QACxD,YAAY,KAAK,MAAM,OAAO;MAChC,CAAC;AAED,UAAI,WAAW;AACf,UAAI,CAAC,YAAY,OAAO,aAAa,UAAU;AAC7C,mBAAW,IAAI,IAAI,QAAQ,GAAG,EAAE;MAClC;AAEA,YAAM,SAAS,QAAQ;AACvB,YAAM,QAAQ,KAAK,OAAO,UAAU,MAAwB;AAC5D,UAAI,CAAC,OAAO;AACV,cAAME,YAAW,IAAI,SAAS,4BAA4B,QAAQ,IAAI;UACpE,QAAQ;QACV,CAAC;AACD,eAAO,KAAK;UACV,iBAAiB,0BAA0B,EAAE,UAAAA,UAAS,CAAC;QACzD;MACF;AACA,YAAM,CAAC,UAAU,SAAS,KAAK,IAAI;AACnC,YAAM,WAAW,MAAM,SAAS,iBAAiB,OAAO;AACxD,aAAO,KAAK;QACV,iBAAiB,0BAA0B,EAAE,SAAS,CAAC;MACzD;IACF,CAAA;EAAA;AACF;;;;;;ACjRA,eAAsB,qBAKpB,mBAMiD;AACjD,QAAM,UAAU,mBAAmB,iBAAiB;AACpD,SAAO,MAAM,oBAAoB,4BAA4B;IAC3D,GAAG;IACH;EACF,CAAC;AACH;AAyFA,IAAM,qBAAN,MAAsE;EAWpE,YAAY,YAAwB,MAAc;AAPlD,IAAAC,gBAAA,MAAA,aAAA;AAKA,IAAAA,gBAAA,MAAA,OAAA;AAGE,SAAK,cAAc;AACnB,SAAK,QAAQ;AACb,qBAAiB,MAAM,6BAA6B,IAAI,EAAE;EAC5D;EAEA,IAAI,UAAkD;AACpD,WAAO,cAAc,KAAK,OAAO,CAAC,CAAC;EACrC;AACF;AAEA,SAAS,cAAc,MAAc,WAA0B;AAC7D,QAAM,UAA6B;IACjC,IAAI,GAAG,MAAuB;AAC5B,UAAI,OAAO,SAAS,UAAU;AAC5B,cAAM,WAAW,CAAC,GAAG,WAAW,IAAI;AACpC,eAAO,cAAc,MAAM,QAAQ;MACrC,WAAW,SAAS,iBAAiB;AACnC,YAAI,YAAY,6BAA6B,IAAI;AACjD,mBAAW,QAAQ,WAAW;AAC5B,uBAAa,IAAI,IAAI;QACvB;AACA,eAAO;MACT,OAAO;AACL,eAAO;MACT;IACF;EACF;AACA,SAAO,IAAI,MAAM,CAAC,GAAG,OAAO;AAC9B;AAEA,SAAS,IAEP,YACA,SAGgC;AAEhC,QAAM,8BACJ;AACF,MAAI,OAAO,4BAA4B,4BAA4B,UAAU;AAC3E,UAAM,IAAI;MACR;IACF;EACF;AACA,QAAM,QACJ,mCAAS;EAET,4BAA4B;EAE5B,4BAA4B,wBAAwB,MAAM,GAAG,EAAE,IAAI;AACrE,OAAK,iBAAiB,KAAK,CAAC,MAAM,6BAA6B,CAAC,CAAC,CAAC;AAClE,SAAO,IAAI,mBAAmB,YAAY,IAAI;AAChD;AAgBA,SAAS,uBAEgB;AACvB,QAAM,iBAAiB,EAAE,MAAM,MAAe;AAC9C,QAAM,kBAAkB,yBAAyB,KAAK,gBAAgB;AACtE,SAAO;IACL;IACA;IACA,YAAY,CAAC;IACb,SAAS,oBAAoB,KAAK,WAAW;EAC/C;AACF;AAEA,SAAS,oBAAoB,MAAuB;AAClD,QAAM,SAAgB,CAAC;AACvB,aAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,IAAI,GAAG;AAC/C,QAAI;AACJ,QAAI,OAAO,UAAU,UAAU;AAC7B,aAAO,EAAE,MAAM,QAAQ,MAAM,MAAM;IACrC,OAAO;AACL,aAAO,oBAAoB,KAAK;IAClC;AACA,WAAO,KAAK,CAAC,KAAK,IAAI,CAAC;EACzB;AACA,SAAO,EAAE,MAAM,UAAU,OAAO;AAClC;AAEA,SAAS,yBACP,iBASE;AACF,SAAO,gBAAgB,IAAI,CAAC,CAAC,MAAM,YAAY,CAAC,MAAM;AACpD,QAAI,OAA4D;AAChE,QAAI,MAAM,MAAM;AACd,aAAO,CAAC;AACR,iBAAW,CAACC,OAAM,KAAK,KAAK,OAAO,QAAQ,CAAC,GAAG;AAC7C,YAAI,UAAU,QAAW;AACvB,eAAK,KAAK;YACRA;YACA,EAAE,MAAM,SAAS,OAAO,KAAK,UAAU,aAAa,KAAK,CAAC,EAAE;UAC9D,CAAC;QACH;MACF;IACF;AAEA,UAAM,OAAO,WAAW;AACxB,QAAI,CAAC;AACH,YAAM,IAAI;QACR,gDACE,KAAK,UAAU,YAAY,MAAM,CAAC;MACtC;AAEF,WAAO;MACL;MACA;MACA;IACF;EACF,CAAC;AACH;AAEA,SAAS,6BAEsB;AAC7B,QAAM,OAAqD,OAAO;IAChE,KAAK;EACP,EAAE,IAAI,CAAC,CAAC,MAAM,SAAS,MAAM;IAC3B;IACA;MACE,MAAM;MACN,OAAO,KAAK,UAAU,UAAU,IAAI;IACtC;EACF,CAAC;AACD,QAAM,iBAA0C;IAC9C,MAAM;IACN,MAAM,KAAK;IACX;EACF;AACA,QAAM,kBAAkB,yBAAyB,KAAK,gBAAgB;AACtE,SAAO;IACL,MAAM,KAAK;IACX;IACA;IACA,YAAY,CAAC;IACb,SAAS,oBAAoB,KAAK,WAAW;EAC/C;AACF;AA0BO,SAAS,gBACd,MAC8B;AAC9B,QAAM,MAAkC;IACtC,SAAS;IACT,OAAO;IACP,OAAO,CAAC;IACR,kBAAkB,CAAC;IACnB,aAAa,CAAC;IACd,kBAAkB,CAAC;IAEnB,QAAQ;IACR;;IAGA,GAAI,CAAC;EACP;AACA,SAAO;AACT;AAQO,SAAS,YAA2B;AACzC,QAAM,MAA4B;IAChC,SAAS;IACT,kBAAkB,CAAC;IACnB,aAAa,CAAC;IAEd,QAAQ;IACR;EACF;AACA,SAAO;AACT;AAYO,SAAS,4BACd,aACuB;AACvB,SAAO;IACL,CAAC,eAAe,GAAG,0CAA0C,WAAW;EAC1E;AACF;AAEA,SAAS,sBACP,MACA,WACoB;AACpB,QAAM,UAAgC;IACpC,IAAI,GAAG,MAAuB;AAC5B,UAAI,OAAO,SAAS,UAAU;AAC5B,cAAM,WAAW,CAAC,GAAG,WAAW,IAAI;AACpC,eAAO,sBAAsB,MAAM,QAAQ;MAC7C,WAAW,SAAS,iBAAiB;AACnC,YAAI,UAAU,SAAS,GAAG;AACxB,gBAAM,QAAQ,CAAC,MAAM,GAAG,SAAS,EAAE,KAAK,GAAG;AAC3C,gBAAM,IAAI;YACR,4CAA4C,IAAI,4CAA4C,KAAK;UACnG;QACF;AACA,eAAO,+BAA+B,UAAU,KAAK,GAAG;MAC1D,OAAO;AACL,eAAO;MACT;IACF;EACF;AACA,SAAO,IAAI,MAAM,CAAC,GAAG,OAAO;AAC9B;AAEO,IAAM,oBAAoB,MAAM,sBAAsB,cAAc,CAAC,CAAC;;;;;;AC5RtE,IAAM,kBAAN,MAKL;;;;EAUA,YAAY,cAA4B;AATxC,IAAAC,gBAAA,MAAQ,SAAA;AACR,IAAAA,gBAAA,MAAQ,eAAA;AACR,IAAAA,gBAAA,MAAQ,eAAA;AAER,IAAAA,gBAAA,MAAA,WAAA;AAME,SAAK,UAAU,CAAC;AAChB,SAAK,gBAAgB,CAAC;AACtB,SAAK,gBAAgB,CAAC;AACtB,SAAK,YAAY;EACnB;;;;;;;;;;;EAYA,MAKE,MACA,QAcA;AACA,SAAK,QAAQ,KAAK,EAAE,iBAAiB,MAAM,OAAO,CAAC;AACnD,WAAO;EACT;;;;;;;;;;EAWA,YAKE,MACA,aAiBA;AACA,SAAK,cAAc,KAAK;MACtB,iBAAiB;MACjB,aAAa,YAAY;MACzB,cAAc,YAAY,gBAAgB,CAAC;IAC7C,CAAC;AACD,WAAO;EACT;;;;;;;;;;EAWA,YAKE,MACA,aAgBA;AACA,SAAK,cAAc,KAAK;MACtB,iBAAiB;MACjB,aAAa,YAAY;MACzB,YAAY,YAAY;MACxB,cAAc,YAAY,gBAAgB,CAAC;IAC7C,CAAC;AACD,WAAO;EACT;;;;EAKU,OAKR;AACA,WAAO;EACT;;;;;;;EAOA,SAAS;AACP,UAAM,eAAe,KAAK,UAAU;AACpC,QAAI,OAAO,iBAAiB,UAAU;AACpC,YAAM,IAAI;QACR;MACF;IACF;AAEA,WAAO;MACL,SAAS,KAAK;MACd,eAAe,KAAK;MACpB,eAAe,KAAK;MACpB;IACF;EACF;AACF;AA4DO,SAAS,YAId,gBAAgE;AAChE,MAAI,YAAY,cAAc,GAAG;AAC/B,WAAO,IAAI,gBAAgB,cAAc;EAC3C,OAAO;AACL,WAAO,IAAI,gBAAgB,EAAE,OAAO,cAAc,CAAC;EACrD;AACF;AAkBO,IAAM,mBAAN,MAGL;;;;EAQA,YAAY,QAAgB,SAAiD;AAP7E,IAAAA,gBAAA,MAAO,QAAA;AACP,IAAAA,gBAAA,MAAO,sBAAA;AACP,IAAAA,gBAAA,MAAiB,kBAAA;AAMf,SAAK,SAAS;AACd,SAAK,oBACH,mCAAS,sBAAqB,SAAY,OAAO,QAAQ;EAC7D;;;;;;;EAQA,SAAiB;AACf,WAAO,KAAK,UAAU;MACpB,QAAQ,OAAO,QAAQ,KAAK,MAAM,EAAE,IAAI,CAAC,CAAC,WAAW,UAAU,MAAM;AACnE,cAAM,EAAE,SAAS,eAAe,eAAe,aAAa,IAC1D,WAAW,OAAO;AACpB,eAAO;UACL;UACA;UACA;UACA;UACA;QACF;MACF,CAAC;MACD,kBAAkB,KAAK;IACzB,CAAC;EACH;AACF;AAkEO,SAAS,aAId,QACA,SACgD;AAChD,SAAO,IAAI,iBAAiB,QAAQ,OAAO;AAC7C;AA2CA,IAAM,gBAAgB,aAAa;EACjC,sBAAsB,YAAY;IAChC,MAAM,EAAE,OAAO;IACf,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC;IACrB,eAAe,EAAE,QAAQ;IACzB,eAAe,EAAE,SAAS,EAAE,QAAQ,CAAC;IACrC,OAAO,EAAE;MACP,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,SAAS,EAAE,CAAC;MACvC,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,YAAY,EAAE,CAAC;MAC1C,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,SAAS,EAAE,CAAC;MACvC,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,QAAQ,GAAG,OAAO,EAAE,OAAO,EAAE,CAAC;MACzD,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,UAAU,EAAE,CAAC;IAC1C;EACF,CAAC;EACD,UAAU,YAAY;IACpB,QAAQ,EAAE,OAAO;IACjB,MAAM,EAAE,QAAQ;IAChB,aAAa,EAAE,SAAS,EAAE,OAAO,CAAC;EACpC,CAAC;AACH,CAAC;", "names": ["__publicField", "__publicField", "ExpressionImpl", "__publicField", "serializeExpression", "filterBuilderImpl", "__publicField", "__publicField", "__publicField", "__publicField", "__publicField", "serializeExpression", "filterBuilderImpl", "syscallArgs", "args", "v", "syscallArgs", "__publicField", "__publicField", "_a", "response", "__publicField", "name", "__publicField"]}