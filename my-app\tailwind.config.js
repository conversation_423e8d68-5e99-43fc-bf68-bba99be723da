/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: ['class'],
  content: [
    './index.html',
    './src/**/*.{js,ts,jsx,tsx}',
  ],
  // Disable all of Tailwind's default styles
  corePlugins: {
    preflight: false,
    // Disable all other core plugins we're not using
    // to reduce the CSS bundle size
    ...Object.fromEntries(
      [
        'container', 'accessibility', 'alignItems', 'alignSelf', 'animation', 'appearance',
        'backdropBlur', 'backdropBrightness', 'backdropContrast', 'backdropFilter', 'backdropGrayscale',
        'backdropHueRotate', 'backdropInvert', 'backdropOpacity', 'backdropSaturate', 'backdropSepia',
        'backgroundAttachment', 'backgroundClip', 'backgroundColor', 'backgroundImage', 'backgroundOpacity',
        'backgroundPosition', 'backgroundRepeat', 'backgroundSize', 'blur', 'borderCollapse', 'borderColor',
        'borderOpacity', 'borderRadius', 'borderSpacing', 'borderStyle', 'borderWidth', 'boxShadow',
        'boxSizing', 'brightness', 'clear', 'cursor', 'display', 'divideColor', 'divideOpacity', 'divideStyle',
        'divideWidth', 'dropShadow', 'fill', 'filter', 'flex', 'flexBasis', 'flexDirection', 'flexGrow',
        'flexShrink', 'flexWrap', 'float', 'fontFamily', 'fontSize', 'fontSmoothing', 'fontStyle', 'fontVariantNumeric',
        'fontWeight', 'gap', 'gradientColorStops', 'gradientColorStops', 'grayscale', 'gridAutoColumns',
        'gridAutoFlow', 'gridAutoRows', 'gridColumn', 'gridColumnEnd', 'gridColumnStart', 'gridRow',
        'gridRowEnd', 'gridRowStart', 'gridTemplateColumns', 'gridTemplateRows', 'height', 'inset',
        'invert', 'isolation', 'justifyContent', 'justifyItems', 'justifySelf', 'letterSpacing',
        'lineHeight', 'listStylePosition', 'listStyleType', 'margin', 'maxHeight', 'maxWidth', 'minHeight',
        'minWidth', 'mixBlendMode', 'objectFit', 'objectPosition', 'opacity', 'order', 'outline', 'overflow',
        'overscrollBehavior', 'padding', 'placeContent', 'placeItems', 'placeSelf', 'placeholderColor',
        'placeholderOpacity', 'pointerEvents', 'position', 'resize', 'ringColor', 'ringOffsetColor',
        'ringOffsetWidth', 'ringOpacity', 'ringWidth', 'rotate', 'saturate', 'scale', 'sepia', 'skew',
        'space', 'stroke', 'strokeWidth', 'tableLayout', 'textAlign', 'textColor', 'textDecoration',
        'textOpacity', 'textOverflow', 'textTransform', 'transform', 'transformOrigin', 'transitionDelay',
        'transitionDuration', 'transitionProperty', 'transitionTimingFunction', 'translate', 'userSelect',
        'verticalAlign', 'visibility', 'whitespace', 'width', 'wordBreak', 'zIndex',
      ].map(plugin => [plugin, false])
    ),
  },
  theme: {
    container: {
      center: true,
      padding: '2rem',
      screens: {
        '2xl': '1400px',
      },
    },
    extend: {
      colors: {
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))',
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))',
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))',
        },
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))',
        },
      },
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
      },
      keyframes: {
        'accordion-down': {
          from: { height: 0 },
          to: { height: 'var(--radix-accordion-content-height)' },
        },
        'accordion-up': {
          from: { height: 'var(--radix-accordion-content-height)' },
          to: { height: 0 },
        },
      },
      animation: {
        'accordion-down': 'accordion-down 0.2s ease-out',
        'accordion-up': 'accordion-up 0.2s ease-out',
      },
    },
  },
  plugins: [require('tailwindcss-animate'), require('@tailwindcss/nesting'),],
}
