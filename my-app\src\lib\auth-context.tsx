import { createContext, useContext, useEffect, useState } from 'react';
import { useConvexAuth } from 'convex/react';

type User = {
  _id: string;
  name: string;
  email: string;
  avatar?: string;
} | null;

type AuthContextType = {
  user: User;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<void>;
  register: (name: string, email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const { isAuthenticated, isLoading: isAuthLoading } = useConvexAuth();
  const [user, setUser] = useState<User>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Load user data when authentication state changes
    const loadUser = async () => {
      try {
        if (isAuthLoading) return;
        
        if (isAuthenticated) {
          // TODO: Fetch user data from Convex
          // const userData = await fetchUserData();
          // setUser(userData);
        } else {
          setUser(null);
        }
      } catch (error) {
        console.error('Failed to load user:', error);
        setUser(null);
      } finally {
        setIsLoading(false);
      }
    };

    loadUser();
  }, [isAuthenticated, isAuthLoading]);

  const login = async (email: string, password: string) => {
    // TODO: Implement login with Convex
    console.log('Login attempt with:', { email });
  };

  const register = async (name: string, email: string, password: string) => {
    // TODO: Implement registration with Convex
    console.log('Register attempt:', { name, email });
  };

  const logout = async () => {
    // TODO: Implement logout with Convex
    setUser(null);
  };

  return (
    <AuthContext.Provider value={{ user, isLoading, login, register, logout }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
