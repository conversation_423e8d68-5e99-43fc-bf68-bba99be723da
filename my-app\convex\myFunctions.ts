import { v } from "convex/values";
import { query, mutation } from "./_generated/server";
import { Id } from "./_generated/dataModel";

// Write your Convex functions in any file inside this directory (`convex`).
// See https://docs.convex.dev/functions for more.

// Example query to get recent messages for a chat
export const getRecentMessages = query({
  args: {
    chatId: v.string(),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const messages = await ctx.db
      .query("messages")
      .withIndex("by_chat", (q) => q.eq("chatId", args.chatId))
      .order("desc")
      .take(args.limit || 50);
    
    return messages.reverse();
  },
});

// Example mutation to update user's last seen time
export const updateLastSeen = mutation({
  args: {
    userId: v.string(),
    name: v.string(),
    email: v.string(),
  },
  handler: async (ctx, args) => {
    const user = await ctx.db
      .query("users")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .first();

    if (user) {
      await ctx.db.patch(user._id, { lastSeen: Date.now() });
    } else {
      await ctx.db.insert("users", {
        userId: args.userId,
        name: args.name,
        email: args.email,
        lastSeen: Date.now(),
      });
    }
  },
});
