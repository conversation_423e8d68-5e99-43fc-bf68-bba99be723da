{"name": "simplechat", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "npm-run-all --parallel dev:frontend dev:backend", "dev:frontend": "vite --open", "dev:backend": "convex dev", "predev": "convex dev --until-success && convex dashboard", "build": "tsc -b && vite build", "lint": "tsc && eslint .  --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@radix-ui/react-dropdown-menu": "^2.1.15", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "convex": "^1.23.0", "lucide-react": "^0.513.0", "next-themes": "^0.4.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hot-toast": "^2.5.2", "react-router-dom": "^7.6.2", "tailwind-merge": "^3.3.0"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/node": "^22.15.29", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.5.1", "autoprefixer": "^10.4.21", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "npm-run-all": "^4.1.5", "postcss": "^8.4.47", "prettier": "^3.5.3", "tailwindcss": "^3.4.15", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.3.5"}}