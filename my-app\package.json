{"name": "simplechat", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "npm-run-all --parallel dev:frontend dev:backend", "dev:frontend": "vite --open", "dev:backend": "convex dev", "predev": "convex dev --until-success && convex dashboard", "build": "tsc -b && vite build", "lint": "tsc && eslint .  --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@auth0/auth0-react": "^2.3.0", "@emotion/react": "^11.14.0", "@radix-ui/react-dropdown-menu": "^2.1.15", "@types/react-router-dom": "^5.3.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "convex": "^1.23.0", "lucide-react": "^0.513.0", "next-themes": "^0.4.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hot-toast": "^2.5.2", "react-router-dom": "^7.6.2", "tailwind-merge": "^3.3.0"}, "devDependencies": {"@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-decorators": "^7.27.1", "@babel/plugin-transform-runtime": "^7.27.4", "@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@babel/runtime": "^7.27.4", "@eslint/js": "^9.21.0", "@tailwindcss/nesting": "^0.0.0-insiders.565cd3e", "@tailwindcss/postcss": "^4.1.8", "@tailwindcss/postcss7-compat": "^2.2.17", "@tailwindcss/vite": "^4.1.8", "@types/node": "^22.15.29", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.5.1", "autoprefixer": "^10.4.21", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "npm-run-all": "^4.1.5", "postcss": "^7.0.39", "postcss-import": "^16.1.0", "postcss-nested": "^7.0.2", "postcss-nesting": "^13.0.1", "prettier": "^3.5.3", "tailwindcss": "^4.1.8", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.3.5"}}