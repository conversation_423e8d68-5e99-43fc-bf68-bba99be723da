# Real-Time Chat Application

A modern, real-time chat application built with React, TypeScript, and Convex.

## Features

- 🔐 User authentication
- 💬 Real-time messaging
- 👥 Multiple chat rooms
- 📜 Message history
- 📱 Responsive design
- ⚡ Blazing fast performance with Vite

## Tech Stack

- **Frontend**: React 19, TypeScript, Vite
- **Styling**: Tailwind CSS
- **Backend**: Convex (Database & Serverless Functions)
- **Authentication**: Convex Auth
- **Real-time**: Convex Subscriptions

## Getting Started

### Prerequisites

- Node.js 18+ and npm
- Convex account (sign up at [convex.dev](https://www.convex.dev/))

### Installation

1. Clone the repository
   ```bash
   git clone <repository-url>
   cd my-chat-app
   ```

2. Install dependencies
   ```bash
   npm install
   ```

3. Set up environment variables
   ```bash
   cp .env.example .env.local
   ```
   Update the values in `.env.local` with your Convex deployment URL and other configuration.

4. Start the development server
   ```bash
   npm run dev
   ```

5. Open [http://localhost:3000](http://localhost:3000) in your browser

## Project Structure

```
src/
├── assets/            # Static assets
├── components/         # Reusable UI components
│   ├── auth/          # Authentication components
│   ├── chat/          # Chat components
│   └── ui/            # Generic UI components
├── lib/               # Utility functions
└── pages/             # Page components
```

## Development

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

## Deployment

This application is configured for deployment to Convex. Push your code to your Git repository and connect it to your Convex project.

## License

MIT
