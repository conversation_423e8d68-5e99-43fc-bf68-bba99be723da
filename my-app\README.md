# Chat Application - Development Directory

This directory contains the main chat application code.

## 🚀 Quick Start

From this directory, run:
```bash
npm install
npm run dev
```

The application will start at http://localhost:3000

## 📁 Directory Structure

```
src/
├── components/         # Reusable UI components
│   ├── auth/          # Authentication components
│   ├── chat/          # Chat-specific components
│   ├── layout/        # Layout components
│   └── ui/            # Generic UI components (buttons, forms, etc.)
├── lib/               # Utility functions & contexts
├── pages/             # Page components (Chat, Settings, etc.)
└── main.tsx           # Application entry point
```

```
convex/                # Backend functions & database schema
├── _generated/        # Auto-generated Convex files
├── messages.ts        # Message-related functions
├── myFunctions.ts     # General utility functions
└── schema.ts          # Database schema definition
```

## 🛠 Development Commands

- `npm run dev` - Start both frontend and backend
- `npm run dev:frontend` - Start only Vite dev server
- `npm run dev:backend` - Start only Convex backend
- `npm run build` - Build for production
- `npm run lint` - Run ESLint

## 📚 Documentation

For complete setup instructions and project overview, see the main [README.md](../README.md) in the root directory.
