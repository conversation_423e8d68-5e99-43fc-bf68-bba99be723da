# Real-Time Chat Application

A modern, real-time chat application built with React, TypeScript, Vite, and Convex.

## ✅ Status: **WORKING**

The project is now fully functional and running successfully!

## 🚀 Features

- 💬 Real-time messaging with Convex
- 🎨 Modern UI with Tailwind CSS
- 📱 Responsive design (desktop & mobile)
- ⚡ Fast development with Vite
- 🔒 Type-safe with TypeScript
- 🌙 Dark/Light theme support
- 👥 Multiple chat rooms
- 📜 Message history

## 🛠 Tech Stack

- **Frontend**: React 19, TypeScript, Vite
- **Styling**: Tailwind CSS
- **Backend**: Convex (Database & Serverless Functions)
- **Real-time**: Convex Subscriptions
- **UI Components**: Radix UI primitives

## 📋 Prerequisites

- Node.js 18+ and npm
- Convex account (optional - works with local development)

## 🚀 Quick Start

1. **Navigate to the app directory**:
   ```bash
   cd my-app
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Start the development server**:
   ```bash
   npm run dev
   ```

4. **Open your browser**: http://localhost:3000

The app will automatically start both the frontend (Vite) and backend (Convex) servers.

## 📁 Project Structure

```
my-app/
├── src/
│   ├── components/         # Reusable UI components
│   │   ├── auth/          # Authentication components
│   │   ├── chat/          # Chat-specific components
│   │   ├── layout/        # Layout components
│   │   └── ui/            # Generic UI components
│   ├── lib/               # Utility functions & contexts
│   ├── pages/             # Page components
│   └── main.tsx           # Application entry point
├── convex/                # Backend functions & schema
├── public/                # Static assets
└── docs/                  # Additional documentation
```

## 🎯 Available Scripts

- `npm run dev` - Start development (frontend + backend)
- `npm run dev:frontend` - Start only frontend
- `npm run dev:backend` - Start only Convex backend
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

## 🔧 Development

The application uses a demo user by default, so you can start chatting immediately without setting up authentication.

## 📚 Additional Documentation

- [Project Overview](./my-app/docs/PROJECT_OVERVIEW.md) - Detailed project information
- [Development Roadmap](./my-app/docs/ROADMAP.md) - Future development plans

## 📄 License

MIT
