# SimpleChat

A real-time chat application built with React, TypeScript, Vite, and Convex.

## Project Status

⚠️ **Work in Progress** ⚠️

This project is currently under development. See [STATUS.md](./STATUS.md) for current status and known issues.

## Features

- Real-time messaging
- User authentication with Auth0
- Modern UI with Tailwind CSS
- Type-safe with TypeScript
- Built with Vite for fast development

## Prerequisites

- Node.js (v18 or later)
- npm or yarn
- Convex CLI (for local development)

## Getting Started

1. Clone the repository
2. Install dependencies:
   ```bash
   cd simplechat
   npm install
   ```
3. Set up environment variables (see `.env.example`)
4. Start the development server:
   ```bash
   npm run dev
   ```

## Project Structure

```
simplechat/
├── public/              # Static files
├── src/
│   ├── components/      # Reusable UI components
│   ├── pages/           # Page components
│   ├── styles/          # Global styles
│   ├── App.tsx          # Main App component
│   └── main.tsx         # Application entry point
├── .env                 # Environment variables
├── package.json         # Project dependencies and scripts
├── tailwind.config.js   # Tailwind CSS configuration
├── postcss.config.cjs   # PostCSS configuration
└── tsconfig.json       # TypeScript configuration
```

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

## Contributing

Contributions are welcome! Please open an issue first to discuss what you would like to change.

## License

[MIT](https://choosealicense.com/licenses/mit/)
