import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import { ConvexProvider, ConvexReactClient } from 'convex/react';
import { ThemeProvider } from './components/ui/theme-provider';
import { Auth0Provider } from '@auth0/auth0-react';
import './index.css';
import App from './App';

// Debug: Check if root element exists
const rootElement = document.getElementById('root');
console.log('Root element:', rootElement);
if (!rootElement) {
  console.error('Failed to find the root element');
}

const convex = new ConvexReactClient(import.meta.env.VITE_CONVEX_URL as string);

// Make sure to replace these with your actual Auth0 credentials
const domain = import.meta.env.VITE_AUTH0_DOMAIN;
const clientId = import.meta.env.VITE_AUTH0_CLIENT_ID;

createRoot(rootElement!).render(
  <StrictMode>
    <Auth0Provider
      domain={domain}
      clientId={clientId}
      authorizationParams={{
        redirect_uri: window.location.origin,
      }}
    >
      <ConvexProvider client={convex}>
        <ThemeProvider defaultTheme="system" enableSystem>
          <App />
        </ThemeProvider>
      </ConvexProvider>
    </Auth0Provider>
  </StrictMode>
);
